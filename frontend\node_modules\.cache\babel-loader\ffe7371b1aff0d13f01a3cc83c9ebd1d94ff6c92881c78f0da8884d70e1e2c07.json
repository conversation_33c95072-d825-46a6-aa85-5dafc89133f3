{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add token to requests if available\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle token expiration\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  login: credentials => api.post('/auth/login.php', credentials),\n  register: userData => api.post('/auth/register.php', userData),\n  logout: () => api.post('/auth/logout.php'),\n  getProfile: () => api.get('/auth/profile.php'),\n  updateProfile: userData => api.put('/auth/profile.php', userData)\n};\n\n// Products API\nexport const productsAPI = {\n  getAll: params => api.get('/products/index.php', {\n    params\n  }),\n  getById: id => api.get(`/products/show.php?id=${id}`),\n  create: productData => api.post('/products/create.php', productData),\n  update: (id, productData) => api.put(`/products/update.php?id=${id}`, productData),\n  delete: id => api.delete(`/products/delete.php?id=${id}`),\n  search: query => api.get(`/products/search.php?q=${query}`)\n};\n\n// Categories API\nexport const categoriesAPI = {\n  getAll: () => api.get('/categories/index.php'),\n  getById: id => api.get(`/categories/show.php?id=${id}`),\n  create: categoryData => api.post('/categories/create.php', categoryData),\n  update: (id, categoryData) => api.put(`/categories/update.php?id=${id}`, categoryData),\n  delete: id => api.delete(`/categories/delete.php?id=${id}`)\n};\n\n// Cart API\nexport const cartAPI = {\n  get: () => api.get('/cart/index.php'),\n  add: (productId, quantity = 1) => api.post('/cart/add.php', {\n    product_id: productId,\n    quantity\n  }),\n  update: (productId, quantity) => api.put('/cart/update.php', {\n    product_id: productId,\n    quantity\n  }),\n  remove: productId => api.delete(`/cart/remove.php?product_id=${productId}`),\n  clear: () => api.delete('/cart/clear.php')\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: () => api.get('/orders/index.php'),\n  getById: id => api.get(`/orders/show.php?id=${id}`),\n  create: orderData => api.post('/orders/create.php', orderData),\n  update: (id, orderData) => api.put(`/orders/update.php?id=${id}`, orderData),\n  getUserOrders: () => api.get('/orders/user.php')\n};\n\n// Favorites API\nexport const favoritesAPI = {\n  getAll: () => api.get('/favorites/index.php'),\n  add: productId => api.post('/favorites/add.php', {\n    product_id: productId\n  }),\n  remove: productId => api.delete(`/favorites/remove.php?product_id=${productId}`)\n};\n\n// Contact API\nexport const contactAPI = {\n  send: messageData => api.post('/contact/send.php', messageData),\n  getAll: () => api.get('/contact/index.php'),\n  markAsRead: id => api.put(`/contact/read.php?id=${id}`)\n};\n\n// Admin API\nexport const adminAPI = {\n  getDashboard: () => api.get('/admin/dashboard.php'),\n  getUsers: () => api.get('/admin/users.php'),\n  updateUser: (id, userData) => api.put(`/admin/users.php?id=${id}`, userData),\n  deleteUser: id => api.delete(`/admin/users.php?id=${id}`),\n  getSettings: () => api.get('/admin/settings.php'),\n  updateSettings: settings => api.put('/admin/settings.php', settings)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "response", "error", "_error$response", "status", "removeItem", "window", "location", "href", "Promise", "reject", "authAPI", "login", "credentials", "post", "register", "userData", "logout", "getProfile", "get", "updateProfile", "put", "productsAPI", "getAll", "params", "getById", "id", "productData", "update", "delete", "search", "query", "categoriesAPI", "categoryData", "cartAPI", "add", "productId", "quantity", "product_id", "remove", "clear", "ordersAPI", "orderData", "getUserOrders", "favoritesAPI", "contactAPI", "send", "messageData", "mark<PERSON><PERSON><PERSON>", "adminAPI", "getDashboard", "getUsers", "updateUser", "deleteUser", "getSettings", "updateSettings", "settings"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add token to requests if available\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle token expiration\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: (credentials) => api.post('/auth/login.php', credentials),\n  register: (userData) => api.post('/auth/register.php', userData),\n  logout: () => api.post('/auth/logout.php'),\n  getProfile: () => api.get('/auth/profile.php'),\n  updateProfile: (userData) => api.put('/auth/profile.php', userData),\n};\n\n// Products API\nexport const productsAPI = {\n  getAll: (params) => api.get('/products/index.php', { params }),\n  getById: (id) => api.get(`/products/show.php?id=${id}`),\n  create: (productData) => api.post('/products/create.php', productData),\n  update: (id, productData) => api.put(`/products/update.php?id=${id}`, productData),\n  delete: (id) => api.delete(`/products/delete.php?id=${id}`),\n  search: (query) => api.get(`/products/search.php?q=${query}`),\n};\n\n// Categories API\nexport const categoriesAPI = {\n  getAll: () => api.get('/categories/index.php'),\n  getById: (id) => api.get(`/categories/show.php?id=${id}`),\n  create: (categoryData) => api.post('/categories/create.php', categoryData),\n  update: (id, categoryData) => api.put(`/categories/update.php?id=${id}`, categoryData),\n  delete: (id) => api.delete(`/categories/delete.php?id=${id}`),\n};\n\n// Cart API\nexport const cartAPI = {\n  get: () => api.get('/cart/index.php'),\n  add: (productId, quantity = 1) => api.post('/cart/add.php', { product_id: productId, quantity }),\n  update: (productId, quantity) => api.put('/cart/update.php', { product_id: productId, quantity }),\n  remove: (productId) => api.delete(`/cart/remove.php?product_id=${productId}`),\n  clear: () => api.delete('/cart/clear.php'),\n};\n\n// Orders API\nexport const ordersAPI = {\n  getAll: () => api.get('/orders/index.php'),\n  getById: (id) => api.get(`/orders/show.php?id=${id}`),\n  create: (orderData) => api.post('/orders/create.php', orderData),\n  update: (id, orderData) => api.put(`/orders/update.php?id=${id}`, orderData),\n  getUserOrders: () => api.get('/orders/user.php'),\n};\n\n// Favorites API\nexport const favoritesAPI = {\n  getAll: () => api.get('/favorites/index.php'),\n  add: (productId) => api.post('/favorites/add.php', { product_id: productId }),\n  remove: (productId) => api.delete(`/favorites/remove.php?product_id=${productId}`),\n};\n\n// Contact API\nexport const contactAPI = {\n  send: (messageData) => api.post('/contact/send.php', messageData),\n  getAll: () => api.get('/contact/index.php'),\n  markAsRead: (id) => api.put(`/contact/read.php?id=${id}`),\n};\n\n// Admin API\nexport const adminAPI = {\n  getDashboard: () => api.get('/admin/dashboard.php'),\n  getUsers: () => api.get('/admin/users.php'),\n  updateUser: (id, userData) => api.put(`/admin/users.php?id=${id}`, userData),\n  deleteUser: (id) => api.delete(`/admin/users.php?id=${id}`),\n  getSettings: () => api.get('/admin/settings.php'),\n  updateSettings: (settings) => api.put('/admin/settings.php', settings),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACI,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAC1BM,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACR,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMS,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAW,IAAKxB,GAAG,CAACyB,IAAI,CAAC,iBAAiB,EAAED,WAAW,CAAC;EAChEE,QAAQ,EAAGC,QAAQ,IAAK3B,GAAG,CAACyB,IAAI,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;EAChEC,MAAM,EAAEA,CAAA,KAAM5B,GAAG,CAACyB,IAAI,CAAC,kBAAkB,CAAC;EAC1CI,UAAU,EAAEA,CAAA,KAAM7B,GAAG,CAAC8B,GAAG,CAAC,mBAAmB,CAAC;EAC9CC,aAAa,EAAGJ,QAAQ,IAAK3B,GAAG,CAACgC,GAAG,CAAC,mBAAmB,EAAEL,QAAQ;AACpE,CAAC;;AAED;AACA,OAAO,MAAMM,WAAW,GAAG;EACzBC,MAAM,EAAGC,MAAM,IAAKnC,GAAG,CAAC8B,GAAG,CAAC,qBAAqB,EAAE;IAAEK;EAAO,CAAC,CAAC;EAC9DC,OAAO,EAAGC,EAAE,IAAKrC,GAAG,CAAC8B,GAAG,CAAC,yBAAyBO,EAAE,EAAE,CAAC;EACvDpC,MAAM,EAAGqC,WAAW,IAAKtC,GAAG,CAACyB,IAAI,CAAC,sBAAsB,EAAEa,WAAW,CAAC;EACtEC,MAAM,EAAEA,CAACF,EAAE,EAAEC,WAAW,KAAKtC,GAAG,CAACgC,GAAG,CAAC,2BAA2BK,EAAE,EAAE,EAAEC,WAAW,CAAC;EAClFE,MAAM,EAAGH,EAAE,IAAKrC,GAAG,CAACwC,MAAM,CAAC,2BAA2BH,EAAE,EAAE,CAAC;EAC3DI,MAAM,EAAGC,KAAK,IAAK1C,GAAG,CAAC8B,GAAG,CAAC,0BAA0BY,KAAK,EAAE;AAC9D,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BT,MAAM,EAAEA,CAAA,KAAMlC,GAAG,CAAC8B,GAAG,CAAC,uBAAuB,CAAC;EAC9CM,OAAO,EAAGC,EAAE,IAAKrC,GAAG,CAAC8B,GAAG,CAAC,2BAA2BO,EAAE,EAAE,CAAC;EACzDpC,MAAM,EAAG2C,YAAY,IAAK5C,GAAG,CAACyB,IAAI,CAAC,wBAAwB,EAAEmB,YAAY,CAAC;EAC1EL,MAAM,EAAEA,CAACF,EAAE,EAAEO,YAAY,KAAK5C,GAAG,CAACgC,GAAG,CAAC,6BAA6BK,EAAE,EAAE,EAAEO,YAAY,CAAC;EACtFJ,MAAM,EAAGH,EAAE,IAAKrC,GAAG,CAACwC,MAAM,CAAC,6BAA6BH,EAAE,EAAE;AAC9D,CAAC;;AAED;AACA,OAAO,MAAMQ,OAAO,GAAG;EACrBf,GAAG,EAAEA,CAAA,KAAM9B,GAAG,CAAC8B,GAAG,CAAC,iBAAiB,CAAC;EACrCgB,GAAG,EAAEA,CAACC,SAAS,EAAEC,QAAQ,GAAG,CAAC,KAAKhD,GAAG,CAACyB,IAAI,CAAC,eAAe,EAAE;IAAEwB,UAAU,EAAEF,SAAS;IAAEC;EAAS,CAAC,CAAC;EAChGT,MAAM,EAAEA,CAACQ,SAAS,EAAEC,QAAQ,KAAKhD,GAAG,CAACgC,GAAG,CAAC,kBAAkB,EAAE;IAAEiB,UAAU,EAAEF,SAAS;IAAEC;EAAS,CAAC,CAAC;EACjGE,MAAM,EAAGH,SAAS,IAAK/C,GAAG,CAACwC,MAAM,CAAC,+BAA+BO,SAAS,EAAE,CAAC;EAC7EI,KAAK,EAAEA,CAAA,KAAMnD,GAAG,CAACwC,MAAM,CAAC,iBAAiB;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMY,SAAS,GAAG;EACvBlB,MAAM,EAAEA,CAAA,KAAMlC,GAAG,CAAC8B,GAAG,CAAC,mBAAmB,CAAC;EAC1CM,OAAO,EAAGC,EAAE,IAAKrC,GAAG,CAAC8B,GAAG,CAAC,uBAAuBO,EAAE,EAAE,CAAC;EACrDpC,MAAM,EAAGoD,SAAS,IAAKrD,GAAG,CAACyB,IAAI,CAAC,oBAAoB,EAAE4B,SAAS,CAAC;EAChEd,MAAM,EAAEA,CAACF,EAAE,EAAEgB,SAAS,KAAKrD,GAAG,CAACgC,GAAG,CAAC,yBAAyBK,EAAE,EAAE,EAAEgB,SAAS,CAAC;EAC5EC,aAAa,EAAEA,CAAA,KAAMtD,GAAG,CAAC8B,GAAG,CAAC,kBAAkB;AACjD,CAAC;;AAED;AACA,OAAO,MAAMyB,YAAY,GAAG;EAC1BrB,MAAM,EAAEA,CAAA,KAAMlC,GAAG,CAAC8B,GAAG,CAAC,sBAAsB,CAAC;EAC7CgB,GAAG,EAAGC,SAAS,IAAK/C,GAAG,CAACyB,IAAI,CAAC,oBAAoB,EAAE;IAAEwB,UAAU,EAAEF;EAAU,CAAC,CAAC;EAC7EG,MAAM,EAAGH,SAAS,IAAK/C,GAAG,CAACwC,MAAM,CAAC,oCAAoCO,SAAS,EAAE;AACnF,CAAC;;AAED;AACA,OAAO,MAAMS,UAAU,GAAG;EACxBC,IAAI,EAAGC,WAAW,IAAK1D,GAAG,CAACyB,IAAI,CAAC,mBAAmB,EAAEiC,WAAW,CAAC;EACjExB,MAAM,EAAEA,CAAA,KAAMlC,GAAG,CAAC8B,GAAG,CAAC,oBAAoB,CAAC;EAC3C6B,UAAU,EAAGtB,EAAE,IAAKrC,GAAG,CAACgC,GAAG,CAAC,wBAAwBK,EAAE,EAAE;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMuB,QAAQ,GAAG;EACtBC,YAAY,EAAEA,CAAA,KAAM7D,GAAG,CAAC8B,GAAG,CAAC,sBAAsB,CAAC;EACnDgC,QAAQ,EAAEA,CAAA,KAAM9D,GAAG,CAAC8B,GAAG,CAAC,kBAAkB,CAAC;EAC3CiC,UAAU,EAAEA,CAAC1B,EAAE,EAAEV,QAAQ,KAAK3B,GAAG,CAACgC,GAAG,CAAC,uBAAuBK,EAAE,EAAE,EAAEV,QAAQ,CAAC;EAC5EqC,UAAU,EAAG3B,EAAE,IAAKrC,GAAG,CAACwC,MAAM,CAAC,uBAAuBH,EAAE,EAAE,CAAC;EAC3D4B,WAAW,EAAEA,CAAA,KAAMjE,GAAG,CAAC8B,GAAG,CAAC,qBAAqB,CAAC;EACjDoC,cAAc,EAAGC,QAAQ,IAAKnE,GAAG,CAACgC,GAAG,CAAC,qBAAqB,EAAEmC,QAAQ;AACvE,CAAC;AAED,eAAenE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}