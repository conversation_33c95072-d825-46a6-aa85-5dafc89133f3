<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_error_response('Method not allowed', 405);
}

$input = get_json_input();

// Validate required fields
$required_fields = ['email', 'password'];
$missing_fields = validate_required_fields($input, $required_fields);

if (!empty($missing_fields)) {
    send_error_response('Missing required fields: ' . implode(', ', $missing_fields));
}

$email = sanitize_input($input['email']);
$password = $input['password'];

// Validate email format
if (!validate_email($email)) {
    send_error_response('Invalid email format');
}

// Get database connection
$connection = get_db_connection();

// Check if user exists
$query = "SELECT id, name, email, password, role FROM Users WHERE email = ?";
$result = execute_query($connection, $query, [$email]);
$user = fetch_single($result);

if (!$user) {
    close_db_connection($connection);
    send_error_response('Invalid email or password');
}

// Verify password
if (!verify_password($password, $user['password'])) {
    close_db_connection($connection);
    send_error_response('Invalid email or password');
}

// Generate JWT token
$token = generate_jwt_token($user['id'], $user['email']);

// Remove password from user data
unset($user['password']);

close_db_connection($connection);

// Send success response
send_success_response([
    'token' => $token,
    'user' => $user
], 'Login successful');
?>
