<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_error_response('Method not allowed', 405);
}

// Require admin access
$user = require_admin();

$input = get_json_input();

// Validate required fields
$required_fields = ['name'];
$missing_fields = validate_required_fields($input, $required_fields);

if (!empty($missing_fields)) {
    send_error_response('Missing required fields: ' . implode(', ', $missing_fields));
}

$name = sanitize_input($input['name']);
$description = isset($input['description']) ? sanitize_input($input['description']) : '';
$image = isset($input['image']) ? sanitize_input($input['image']) : '';

$connection = get_db_connection();

// Check if category name already exists
$query = "SELECT id FROM Categories WHERE name = ?";
$result = execute_query($connection, $query, [$name]);
if (fetch_single($result)) {
    close_db_connection($connection);
    send_error_response('Category name already exists');
}

// Insert category
$query = "INSERT INTO Categories (name, description, image) VALUES (?, ?, ?)";
$result = execute_query($connection, $query, [$name, $description, $image]);

if (!$result) {
    close_db_connection($connection);
    send_error_response('Failed to create category');
}

$category_id = get_last_insert_id($connection);

// Get the created category
$query = "SELECT id, name, description, image, created_at FROM Categories WHERE id = ?";
$result = execute_query($connection, $query, [$category_id]);
$category = fetch_single($result);

close_db_connection($connection);

send_success_response(['category' => $category], 'Category created successfully');
?>
