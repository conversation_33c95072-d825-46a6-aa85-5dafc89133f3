import React, { createContext, useContext, useState, useEffect } from 'react';
import { cartAPI } from '../services/api';
import { useAuth } from './AuthContext';

const CartContext = createContext();

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Load cart from localStorage for non-authenticated users
  useEffect(() => {
    if (!isAuthenticated) {
      const localCart = localStorage.getItem('cart');
      if (localCart) {
        try {
          setCartItems(JSON.parse(localCart));
        } catch (error) {
          localStorage.removeItem('cart');
        }
      }
    }
  }, [isAuthenticated]);

  // Load cart from server for authenticated users
  useEffect(() => {
    if (isAuthenticated) {
      loadCart();
    }
  }, [isAuthenticated]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadCart = async () => {
    if (!isAuthenticated) return;
    
    try {
      setLoading(true);
      const response = await cartAPI.get();
      setCartItems(response.data.data.items || []);
    } catch (error) {
      console.error('Failed to load cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId, quantity = 1) => {
    try {
      if (isAuthenticated) {
        await cartAPI.add(productId, quantity);
        await loadCart();
      } else {
        // Handle local cart for non-authenticated users
        const existingItem = cartItems.find(item => item.product_id === productId);
        let newCartItems;
        
        if (existingItem) {
          newCartItems = cartItems.map(item =>
            item.product_id === productId
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        } else {
          newCartItems = [...cartItems, { product_id: productId, quantity }];
        }
        
        setCartItems(newCartItems);
        localStorage.setItem('cart', JSON.stringify(newCartItems));
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to add to cart' 
      };
    }
  };

  const updateCartItem = async (productId, quantity) => {
    try {
      if (isAuthenticated) {
        await cartAPI.update(productId, quantity);
        await loadCart();
      } else {
        const newCartItems = cartItems.map(item =>
          item.product_id === productId
            ? { ...item, quantity }
            : item
        );
        setCartItems(newCartItems);
        localStorage.setItem('cart', JSON.stringify(newCartItems));
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to update cart' 
      };
    }
  };

  const removeFromCart = async (productId) => {
    try {
      if (isAuthenticated) {
        await cartAPI.remove(productId);
        await loadCart();
      } else {
        const newCartItems = cartItems.filter(item => item.product_id !== productId);
        setCartItems(newCartItems);
        localStorage.setItem('cart', JSON.stringify(newCartItems));
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to remove from cart' 
      };
    }
  };

  const clearCart = async () => {
    try {
      if (isAuthenticated) {
        await cartAPI.clear();
      } else {
        localStorage.removeItem('cart');
      }
      setCartItems([]);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to clear cart' 
      };
    }
  };

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  };

  const getCartItemsCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const value = {
    cartItems,
    loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartTotal,
    getCartItemsCount,
    loadCart,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
