{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\components\\\\Layout\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-red-50 pt-12 pb-8 border-t border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid gap-y-8 gap-x-8 md:gap-x-24 lg:gap-x-48 md:grid-cols-2 lg:grid-cols-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo.png\",\n              alt: \"KyoPal\",\n              width: \"50\",\n              height: \"50\",\n              className: \"rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"KyoPal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"Your ultimate destination for premium anime merchandise and collectibles.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://kyopal.com/website/social/facebook\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"footer-link\",\n              title: \"Facebook\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                className: \"h-5 w-5\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 22,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Facebook\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://kyopal.com/website/social/instagram\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"footer-link\",\n              title: \"Instagram\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                className: \"h-5 w-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  width: \"20\",\n                  height: \"20\",\n                  x: \"2\",\n                  y: \"2\",\n                  rx: \"5\",\n                  ry: \"5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"17.5\",\n                  x2: \"17.51\",\n                  y1: \"6.5\",\n                  y2: \"6.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Instagram\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-bold text-lg mb-4 text-gray-900\",\n            children: \"Shop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                className: \"footer-link\",\n                children: \"All Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?category=1\",\n                className: \"footer-link\",\n                children: \"Anime Figures\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?category=2\",\n                className: \"footer-link\",\n                children: \"Manga Books\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?category=3\",\n                className: \"footer-link\",\n                children: \"Dakimakura\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?category=4\",\n                className: \"footer-link\",\n                children: \"Keychains\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?category=6\",\n                className: \"footer-link\",\n                children: \"Cosplay\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-bold text-lg mb-4 text-gray-900\",\n            children: \"Customer Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: \"footer-link\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/faq\",\n                className: \"footer-link\",\n                children: \"FAQ\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                className: \"footer-link\",\n                children: \"Terms & Conditions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/privacy\",\n                className: \"footer-link\",\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                className: \"footer-link\",\n                children: \"About Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-bold text-lg mb-4 text-gray-900\",\n            children: \"Contact Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"22,6 12,13 2,6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"+970 123 456 789\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"12\",\n                  cy: \"10\",\n                  r: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Palestine, West Bank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 pt-8 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-center text-gray-500\",\n          children: [\"\\xA9\", currentYear, \" KyoPal. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "src", "alt", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "title", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "x", "y", "rx", "ry", "x1", "x2", "y1", "y2", "to", "points", "cx", "cy", "r", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/components/Layout/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-red-50 pt-12 pb-8 border-t border-gray-200\">\n      <div className=\"container\">\n        <div className=\"grid gap-y-8 gap-x-8 md:gap-x-24 lg:gap-x-48 md:grid-cols-2 lg:grid-cols-4\">\n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2\">\n              <img src=\"/logo.png\" alt=\"KyoPal\" width=\"50\" height=\"50\" className=\"rounded-full\" />\n              <h2 className=\"text-2xl font-bold text-gray-900\">KyoPal</h2>\n            </div>\n            <p className=\"text-gray-500\">Your ultimate destination for premium anime merchandise and collectibles.</p>\n            <div className=\"flex gap-4\">\n              {/* Social Media Links */}\n              <a href=\"https://kyopal.com/website/social/facebook\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"footer-link\" title=\"Facebook\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"h-5 w-5\">\n                  <path d=\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\"></path>\n                </svg>\n                <span className=\"sr-only\">Facebook</span>\n              </a>\n\n              <a href=\"https://kyopal.com/website/social/instagram\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"footer-link\" title=\"Instagram\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"h-5 w-5\">\n                  <rect width=\"20\" height=\"20\" x=\"2\" y=\"2\" rx=\"5\" ry=\"5\"></rect>\n                  <path d=\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\"></path>\n                  <line x1=\"17.5\" x2=\"17.51\" y1=\"6.5\" y2=\"6.5\"></line>\n                </svg>\n                <span className=\"sr-only\">Instagram</span>\n              </a>\n            </div>\n          </div>\n\n          {/* Shop Links */}\n          <div>\n            <h3 className=\"font-bold text-lg mb-4 text-gray-900\">Shop</h3>\n            <ul className=\"space-y-2\">\n              <li><Link to=\"/products\" className=\"footer-link\">All Products</Link></li>\n              <li><Link to=\"/products?category=1\" className=\"footer-link\">Anime Figures</Link></li>\n              <li><Link to=\"/products?category=2\" className=\"footer-link\">Manga Books</Link></li>\n              <li><Link to=\"/products?category=3\" className=\"footer-link\">Dakimakura</Link></li>\n              <li><Link to=\"/products?category=4\" className=\"footer-link\">Keychains</Link></li>\n              <li><Link to=\"/products?category=6\" className=\"footer-link\">Cosplay</Link></li>\n            </ul>\n          </div>\n\n          {/* Customer Service Links */}\n          <div>\n            <h3 className=\"font-bold text-lg mb-4 text-gray-900\">Customer Service</h3>\n            <ul className=\"space-y-2\">\n              <li><Link to=\"/contact\" className=\"footer-link\">Contact Us</Link></li>\n              <li><Link to=\"/faq\" className=\"footer-link\">FAQ</Link></li>\n              <li><Link to=\"/terms\" className=\"footer-link\">Terms & Conditions</Link></li>\n              <li><Link to=\"/privacy\" className=\"footer-link\">Privacy Policy</Link></li>\n              <li><Link to=\"/about\" className=\"footer-link\">About Us</Link></li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"font-bold text-lg mb-4 text-gray-900\">Contact Info</h3>\n            <ul className=\"space-y-2 text-gray-500\">\n              <li className=\"flex items-center gap-2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"></path>\n                  <polyline points=\"22,6 12,13 2,6\"></polyline>\n                </svg>\n                <span><EMAIL></span>\n              </li>\n              <li className=\"flex items-center gap-2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"></path>\n                </svg>\n                <span>+970 123 456 789</span>\n              </li>\n              <li className=\"flex items-center gap-2\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n                  <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n                </svg>\n                <span>Palestine, West Bank</span>\n              </li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <p className=\"text-center text-gray-500\">&copy;{currentYear} KyoPal. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC/DN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBN,OAAA;QAAKK,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBAEzFN,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBN,OAAA;YAAKK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCN,OAAA;cAAKO,GAAG,EAAC,WAAW;cAACC,GAAG,EAAC,QAAQ;cAACC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACL,SAAS,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFd,OAAA;cAAIK,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNd,OAAA;YAAGK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1Gd,OAAA;YAAKK,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEzBN,OAAA;cAAGe,IAAI,EAAC,4CAA4C;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAACZ,SAAS,EAAC,aAAa;cAACa,KAAK,EAAC,UAAU;cAAAZ,QAAA,gBACrIN,OAAA;gBAAKmB,KAAK,EAAC,4BAA4B;gBAACV,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACU,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACpB,SAAS,EAAC,SAAS;gBAAAC,QAAA,eACnMN,OAAA;kBAAM0B,CAAC,EAAC;gBAAmE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNd,OAAA;gBAAMK,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAQ;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAEJd,OAAA;cAAGe,IAAI,EAAC,6CAA6C;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAACZ,SAAS,EAAC,aAAa;cAACa,KAAK,EAAC,WAAW;cAAAZ,QAAA,gBACvIN,OAAA;gBAAKmB,KAAK,EAAC,4BAA4B;gBAACV,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACU,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACpB,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACnMN,OAAA;kBAAMS,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACiB,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9Dd,OAAA;kBAAM0B,CAAC,EAAC;gBAAiD;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjEd,OAAA;kBAAM+B,EAAE,EAAC,MAAM;kBAACC,EAAE,EAAC,OAAO;kBAACC,EAAE,EAAC,KAAK;kBAACC,EAAE,EAAC;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNd,OAAA;gBAAMK,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNd,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9Dd,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,WAAW;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,sBAAsB;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrFd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,sBAAsB;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,sBAAsB;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,sBAAsB;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,sBAAsB;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNd,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1Ed,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBN,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,UAAU;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtEd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,MAAM;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3Dd,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,QAAQ;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5Ed,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,UAAU;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Ed,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACqC,EAAE,EAAC,QAAQ;gBAAC9B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAQ;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNd,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAIK,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEd,OAAA;YAAIK,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCN,OAAA;cAAIK,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACrCN,OAAA;gBAAKmB,KAAK,EAAC,4BAA4B;gBAACV,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACU,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAAnB,QAAA,gBAC/KN,OAAA;kBAAM0B,CAAC,EAAC;gBAA6E;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7Fd,OAAA;kBAAUoC,MAAM,EAAC;gBAAgB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNd,OAAA;gBAAAM,QAAA,EAAM;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACLd,OAAA;cAAIK,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACrCN,OAAA;gBAAKmB,KAAK,EAAC,4BAA4B;gBAACV,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACU,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAAnB,QAAA,eAC/KN,OAAA;kBAAM0B,CAAC,EAAC;gBAA+R;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5S,CAAC,eACNd,OAAA;gBAAAM,QAAA,EAAM;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACLd,OAAA;cAAIK,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACrCN,OAAA;gBAAKmB,KAAK,EAAC,4BAA4B;gBAACV,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACU,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAAAnB,QAAA,gBAC/KN,OAAA;kBAAM0B,CAAC,EAAC;gBAAgD;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChEd,OAAA;kBAAQqC,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC;gBAAG;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACNd,OAAA;gBAAAM,QAAA,EAAM;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENd,OAAA;QAAKK,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDN,OAAA;UAAGK,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GAAC,MAAM,EAACJ,WAAW,EAAC,+BAA6B;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC0B,EAAA,GA7FIvC,MAAM;AA+FZ,eAAeA,MAAM;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}