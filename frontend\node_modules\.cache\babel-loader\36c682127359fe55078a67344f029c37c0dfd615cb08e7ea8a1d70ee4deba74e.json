{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\components\\\\AdminRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isAdmin,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 12\n    }, this);\n  }\n  if (!isAdmin) {\n    // Redirect to home page if not admin\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(AdminRoute, \"yEh7LKoC3G0LVVV9gFErT07ugdk=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = AdminRoute;\nexport default AdminRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "AdminRoute", "children", "_s", "isAuthenticated", "isAdmin", "loading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/components/AdminRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst AdminRoute = ({ children }) => {\n  const { isAuthenticated, isAdmin, loading } = useAuth();\n  const location = useLocation();\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  if (!isAdmin) {\n    // Redirect to home page if not admin\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return children;\n};\n\nexport default AdminRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EACvD,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,IAAIS,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKQ,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DF,OAAA;QAAKQ,SAAS,EAAC;MAA+D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CAAC;EAEV;EAEA,IAAI,CAACR,eAAe,EAAE;IACpB;IACA,oBAAOJ,OAAA,CAACJ,QAAQ;MAACiB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,IAAI,CAACP,OAAO,EAAE;IACZ;IACA,oBAAOL,OAAA,CAACJ,QAAQ;MAACiB,EAAE,EAAC,GAAG;MAACG,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACC,EAAA,CAvBIF,UAAU;EAAA,QACgCH,OAAO,EACpCD,WAAW;AAAA;AAAAoB,EAAA,GAFxBhB,UAAU;AAyBhB,eAAeA,UAAU;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}