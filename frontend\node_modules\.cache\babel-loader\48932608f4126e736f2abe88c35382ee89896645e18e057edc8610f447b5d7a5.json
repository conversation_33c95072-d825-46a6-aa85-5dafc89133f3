{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        setUser(parsedUser);\n        setIsAuthenticated(true);\n      } catch (error) {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async credentials => {\n    try {\n      const response = await authAPI.login(credentials);\n      const {\n        token,\n        user: userData\n      } = response.data.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await authAPI.register(userData);\n      const {\n        token,\n        user: newUser\n      } = response.data.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(newUser));\n      setUser(newUser);\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n  const updateProfile = async userData => {\n    try {\n      const response = await authAPI.updateProfile(userData);\n      const updatedUser = response.data.data.user;\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n      setUser(updatedUser);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Profile update failed'\n      };\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "error", "removeItem", "login", "credentials", "response", "data", "setItem", "stringify", "success", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "message", "register", "newUser", "_error$response3", "_error$response3$data", "logout", "console", "updateProfile", "updatedUser", "_error$response4", "_error$response4$data", "value", "isAdmin", "role", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n    \n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        setUser(parsedUser);\n        setIsAuthenticated(true);\n      } catch (error) {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (credentials) => {\n    try {\n      const response = await authAPI.login(credentials);\n      const { token, user: userData } = response.data.data;\n      \n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      \n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.error || error.response?.data?.message || 'Login failed'\n      };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await authAPI.register(userData);\n      const { token, user: newUser } = response.data.data;\n      \n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(newUser));\n      setUser(newUser);\n      setIsAuthenticated(true);\n      \n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Registration failed' \n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n\n  const updateProfile = async (userData) => {\n    try {\n      const response = await authAPI.updateProfile(userData);\n      const updatedUser = response.data.data.user;\n      \n      localStorage.setItem('user', JSON.stringify(updatedUser));\n      setUser(updatedUser);\n      \n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Profile update failed' \n      };\n    }\n  };\n\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAdmin: user?.role === 'admin',\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd,MAAMkB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE7C,IAAIF,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;QACvCR,OAAO,CAACS,UAAU,CAAC;QACnBL,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAX,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM5B,OAAO,CAAC0B,KAAK,CAACC,WAAW,CAAC;MACjD,MAAM;QAAEV,KAAK;QAAEN,IAAI,EAAES;MAAS,CAAC,GAAGQ,QAAQ,CAACC,IAAI,CAACA,IAAI;MAEpDX,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEb,KAAK,CAAC;MACpCC,YAAY,CAACY,OAAO,CAAC,MAAM,EAAER,IAAI,CAACS,SAAS,CAACX,QAAQ,CAAC,CAAC;MACtDR,OAAO,CAACQ,QAAQ,CAAC;MACjBJ,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAEgB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdR,KAAK,EAAE,EAAAS,eAAA,GAAAT,KAAK,CAACI,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBV,KAAK,OAAAW,gBAAA,GAAIX,KAAK,CAACI,QAAQ,cAAAO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,uBAApBA,qBAAA,CAAsBC,OAAO,KAAI;MACzE,CAAC;IACH;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOlB,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM5B,OAAO,CAACsC,QAAQ,CAAClB,QAAQ,CAAC;MACjD,MAAM;QAAEH,KAAK;QAAEN,IAAI,EAAE4B;MAAQ,CAAC,GAAGX,QAAQ,CAACC,IAAI,CAACA,IAAI;MAEnDX,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEb,KAAK,CAAC;MACpCC,YAAY,CAACY,OAAO,CAAC,MAAM,EAAER,IAAI,CAACS,SAAS,CAACQ,OAAO,CAAC,CAAC;MACrD3B,OAAO,CAAC2B,OAAO,CAAC;MAChBvB,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAEgB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLT,OAAO,EAAE,KAAK;QACdR,KAAK,EAAE,EAAAgB,gBAAA,GAAAhB,KAAK,CAACI,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMK,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM1C,OAAO,CAAC0C,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;MAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MAC/Bb,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAM4B,aAAa,GAAG,MAAOxB,QAAQ,IAAK;IACxC,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM5B,OAAO,CAAC4C,aAAa,CAACxB,QAAQ,CAAC;MACtD,MAAMyB,WAAW,GAAGjB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAClB,IAAI;MAE3CO,YAAY,CAACY,OAAO,CAAC,MAAM,EAAER,IAAI,CAACS,SAAS,CAACc,WAAW,CAAC,CAAC;MACzDjC,OAAO,CAACiC,WAAW,CAAC;MAEpB,OAAO;QAAEb,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLf,OAAO,EAAE,KAAK;QACdR,KAAK,EAAE,EAAAsB,gBAAA,GAAAtB,KAAK,CAACI,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMW,KAAK,GAAG;IACZrC,IAAI;IACJI,eAAe;IACfF,OAAO;IACPa,KAAK;IACLY,QAAQ;IACRI,MAAM;IACNE,aAAa;IACbK,OAAO,EAAE,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,IAAI,MAAK;EAC1B,CAAC;EAED,oBACEhD,OAAA,CAACC,WAAW,CAACgD,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EAChCA;EAAQ;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC7C,GAAA,CA1GWF,YAAY;AAAAgD,EAAA,GAAZhD,YAAY;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}