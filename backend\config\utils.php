<?php
// Set CORS headers
function set_cors_headers() {
    header("Access-Control-Allow-Origin: http://localhost:3000");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
    header("Access-Control-Allow-Credentials: true");
    
    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Set JSON content type
function set_json_header() {
    header('Content-Type: application/json');
}

// Send JSON response
function send_json_response($data, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode($data);
    exit();
}

// Send error response
function send_error_response($message, $status_code = 400) {
    send_json_response([
        'success' => false,
        'error' => $message
    ], $status_code);
}

// Send success response
function send_success_response($data = [], $message = 'Success') {
    send_json_response([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

// Get JSON input
function get_json_input() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

// Validate required fields
function validate_required_fields($data, $required_fields) {
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing_fields[] = $field;
        }
    }
    
    return $missing_fields;
}

// Sanitize input
function sanitize_input($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Hash password
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Verify password
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

// Generate JWT token (simple implementation)
function generate_jwt_token($user_id, $email) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode([
        'user_id' => $user_id,
        'email' => $email,
        'exp' => time() + (24 * 60 * 60) // 24 hours
    ]);
    
    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    
    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, 'your-secret-key', true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

// Verify JWT token
function verify_jwt_token($token) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }
    
    $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[0]));
    $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1]));
    $signature = $parts[2];
    
    $expectedSignature = hash_hmac('sha256', $parts[0] . "." . $parts[1], 'your-secret-key', true);
    $expectedSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($expectedSignature));
    
    if ($signature !== $expectedSignature) {
        return false;
    }
    
    $payloadData = json_decode($payload, true);
    if ($payloadData['exp'] < time()) {
        return false;
    }
    
    return $payloadData;
}

// Get current user from token
function get_authenticated_user() {
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $auth_header = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $token = $matches[1];
        }
    }
    
    if (!$token) {
        return null;
    }
    
    $payload = verify_jwt_token($token);
    if (!$payload) {
        return null;
    }
    
    // Get user from database
    $connection = get_db_connection();
    $query = "SELECT id, name, email, role FROM Users WHERE id = ? AND email = ?";
    $result = execute_query($connection, $query, [$payload['user_id'], $payload['email']]);
    $user = fetch_single($result);
    close_db_connection($connection);
    
    return $user;
}

// Check if user is authenticated
function require_auth() {
    $user = get_authenticated_user();
    if (!$user) {
        send_error_response('Authentication required', 401);
    }
    return $user;
}

// Check if user is admin
function require_admin() {
    $user = require_auth();
    if ($user['role'] !== 'admin') {
        send_error_response('Admin access required', 403);
    }
    return $user;
}

// Upload file
function upload_file($file, $upload_dir = 'uploads/') {
    if (!isset($file['error']) || is_array($file['error'])) {
        return false;
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    if ($file['size'] > 5000000) { // 5MB limit
        return false;
    }
    
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowed_types)) {
        return false;
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $filepath = $upload_dir . $filename;
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $filepath;
    }
    
    return false;
}

// Format price
function format_price($price) {
    return number_format((float)$price, 2, '.', '');
}

// Calculate shipping fee
function calculate_shipping_fee($region, $total_amount) {
    $connection = get_db_connection();
    $query = "SELECT west_bank_shipping_fee, occupied_territories_shipping_fee, free_shipping_threshold FROM Settings WHERE id = 1";
    $result = execute_query($connection, $query);
    $settings = fetch_single($result);
    close_db_connection($connection);
    
    if (!$settings) {
        // Default values if settings not found
        $settings = [
            'west_bank_shipping_fee' => 20,
            'occupied_territories_shipping_fee' => 50,
            'free_shipping_threshold' => 500
        ];
    }
    
    if ($total_amount >= $settings['free_shipping_threshold']) {
        return 0;
    }
    
    if ($region === 'west_bank') {
        return $settings['west_bank_shipping_fee'];
    } else {
        return $settings['occupied_territories_shipping_fee'];
    }
}
?>
