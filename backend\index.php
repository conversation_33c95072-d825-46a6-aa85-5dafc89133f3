<?php
require_once 'config/utils.php';

set_cors_headers();
set_json_header();

send_success_response([
    'message' => 'KyoPal API is running',
    'version' => '1.0.0',
    'endpoints' => [
        'auth' => [
            'POST /auth/login.php',
            'POST /auth/register.php',
            'POST /auth/logout.php',
            'GET /auth/profile.php',
            'PUT /auth/profile.php'
        ],
        'products' => [
            'GET /products/index.php',
            'GET /products/show.php?id={id}',
            'POST /products/create.php (admin)',
        ],
        'categories' => [
            'GET /categories/index.php',
            'POST /categories/create.php (admin)',
        ],
        'cart' => [
            'GET /cart/index.php (auth)',
            'POST /cart/add.php (auth)',
        ],
        'admin' => [
            'GET /admin/dashboard.php (admin)',
        ]
    ]
]);
?>
