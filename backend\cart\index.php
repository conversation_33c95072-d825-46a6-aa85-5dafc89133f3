<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    send_error_response('Method not allowed', 405);
}

$user = require_auth();

$connection = get_db_connection();

// Get user's cart items
$query = "
    SELECT 
        oi.product_id,
        oi.quantity,
        oi.price,
        p.name,
        p.stock,
        pi.image_path,
        c.name as category_name
    FROM Orders o
    JOIN OrderItems oi ON o.id = oi.order_id
    JOIN Products p ON oi.product_id = p.id
    LEFT JOIN Categories c ON p.category_id = c.id
    LEFT JOIN ProductImages pi ON p.id = pi.product_id
    WHERE o.user_id = ? AND o.status = 'cart'
    GROUP BY oi.product_id
";

$result = execute_query($connection, $query, [$user['id']]);
$cart_items = fetch_all($result);

// Calculate totals
$subtotal = 0;
foreach ($cart_items as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}

close_db_connection($connection);

send_success_response([
    'items' => $cart_items,
    'subtotal' => format_price($subtotal),
    'item_count' => count($cart_items)
]);
?>
