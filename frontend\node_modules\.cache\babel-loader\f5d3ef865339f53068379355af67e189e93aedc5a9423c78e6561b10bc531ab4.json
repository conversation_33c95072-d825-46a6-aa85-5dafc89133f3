{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\contexts\\\\CartContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { cartAPI } from '../services/api';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\nexport const useCart = () => {\n  _s();\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const CartProvider = ({\n  children\n}) => {\n  _s2();\n  const [cartItems, setCartItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    isAuthenticated\n  } = useAuth();\n\n  // Load cart from localStorage for non-authenticated users\n  useEffect(() => {\n    if (!isAuthenticated) {\n      const localCart = localStorage.getItem('cart');\n      if (localCart) {\n        try {\n          setCartItems(JSON.parse(localCart));\n        } catch (error) {\n          localStorage.removeItem('cart');\n        }\n      }\n    }\n  }, [isAuthenticated]);\n\n  // Load cart from server for authenticated users\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadCart();\n    }\n  }, [isAuthenticated]);\n  const loadCart = async () => {\n    if (!isAuthenticated) return;\n    try {\n      setLoading(true);\n      const response = await cartAPI.get();\n      setCartItems(response.data.items || []);\n    } catch (error) {\n      console.error('Failed to load cart:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const addToCart = async (productId, quantity = 1) => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.add(productId, quantity);\n        await loadCart();\n      } else {\n        // Handle local cart for non-authenticated users\n        const existingItem = cartItems.find(item => item.product_id === productId);\n        let newCartItems;\n        if (existingItem) {\n          newCartItems = cartItems.map(item => item.product_id === productId ? {\n            ...item,\n            quantity: item.quantity + quantity\n          } : item);\n        } else {\n          newCartItems = [...cartItems, {\n            product_id: productId,\n            quantity\n          }];\n        }\n        setCartItems(newCartItems);\n        localStorage.setItem('cart', JSON.stringify(newCartItems));\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to add to cart'\n      };\n    }\n  };\n  const updateCartItem = async (productId, quantity) => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.update(productId, quantity);\n        await loadCart();\n      } else {\n        const newCartItems = cartItems.map(item => item.product_id === productId ? {\n          ...item,\n          quantity\n        } : item);\n        setCartItems(newCartItems);\n        localStorage.setItem('cart', JSON.stringify(newCartItems));\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to update cart'\n      };\n    }\n  };\n  const removeFromCart = async productId => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.remove(productId);\n        await loadCart();\n      } else {\n        const newCartItems = cartItems.filter(item => item.product_id !== productId);\n        setCartItems(newCartItems);\n        localStorage.setItem('cart', JSON.stringify(newCartItems));\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to remove from cart'\n      };\n    }\n  };\n  const clearCart = async () => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.clear();\n      } else {\n        localStorage.removeItem('cart');\n      }\n      setCartItems([]);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to clear cart'\n      };\n    }\n  };\n  const getCartTotal = () => {\n    return cartItems.reduce((total, item) => {\n      return total + item.price * item.quantity;\n    }, 0);\n  };\n  const getCartItemsCount = () => {\n    return cartItems.reduce((total, item) => total + item.quantity, 0);\n  };\n  const value = {\n    cartItems,\n    loading,\n    addToCart,\n    updateCartItem,\n    removeFromCart,\n    clearCart,\n    getCartTotal,\n    getCartItemsCount,\n    loadCart\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s2(CartProvider, \"xZJluo03Nl/JbzY9pXUICLcmCI8=\", false, function () {\n  return [useAuth];\n});\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "cartAPI", "useAuth", "jsxDEV", "_jsxDEV", "CartContext", "useCart", "_s", "context", "Error", "CartProvider", "children", "_s2", "cartItems", "setCartItems", "loading", "setLoading", "isAuthenticated", "localCart", "localStorage", "getItem", "JSON", "parse", "error", "removeItem", "loadCart", "response", "get", "data", "items", "console", "addToCart", "productId", "quantity", "add", "existingItem", "find", "item", "product_id", "newCartItems", "map", "setItem", "stringify", "success", "_error$response", "_error$response$data", "message", "updateCartItem", "update", "_error$response2", "_error$response2$data", "removeFromCart", "remove", "filter", "_error$response3", "_error$response3$data", "clearCart", "clear", "_error$response4", "_error$response4$data", "getCartTotal", "reduce", "total", "price", "getCartItemsCount", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/contexts/CartContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { cartAPI } from '../services/api';\nimport { useAuth } from './AuthContext';\n\nconst CartContext = createContext();\n\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n\nexport const CartProvider = ({ children }) => {\n  const [cartItems, setCartItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const { isAuthenticated } = useAuth();\n\n  // Load cart from localStorage for non-authenticated users\n  useEffect(() => {\n    if (!isAuthenticated) {\n      const localCart = localStorage.getItem('cart');\n      if (localCart) {\n        try {\n          setCartItems(JSON.parse(localCart));\n        } catch (error) {\n          localStorage.removeItem('cart');\n        }\n      }\n    }\n  }, [isAuthenticated]);\n\n  // Load cart from server for authenticated users\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadCart();\n    }\n  }, [isAuthenticated]);\n\n  const loadCart = async () => {\n    if (!isAuthenticated) return;\n    \n    try {\n      setLoading(true);\n      const response = await cartAPI.get();\n      setCartItems(response.data.items || []);\n    } catch (error) {\n      console.error('Failed to load cart:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const addToCart = async (productId, quantity = 1) => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.add(productId, quantity);\n        await loadCart();\n      } else {\n        // Handle local cart for non-authenticated users\n        const existingItem = cartItems.find(item => item.product_id === productId);\n        let newCartItems;\n        \n        if (existingItem) {\n          newCartItems = cartItems.map(item =>\n            item.product_id === productId\n              ? { ...item, quantity: item.quantity + quantity }\n              : item\n          );\n        } else {\n          newCartItems = [...cartItems, { product_id: productId, quantity }];\n        }\n        \n        setCartItems(newCartItems);\n        localStorage.setItem('cart', JSON.stringify(newCartItems));\n      }\n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Failed to add to cart' \n      };\n    }\n  };\n\n  const updateCartItem = async (productId, quantity) => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.update(productId, quantity);\n        await loadCart();\n      } else {\n        const newCartItems = cartItems.map(item =>\n          item.product_id === productId\n            ? { ...item, quantity }\n            : item\n        );\n        setCartItems(newCartItems);\n        localStorage.setItem('cart', JSON.stringify(newCartItems));\n      }\n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Failed to update cart' \n      };\n    }\n  };\n\n  const removeFromCart = async (productId) => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.remove(productId);\n        await loadCart();\n      } else {\n        const newCartItems = cartItems.filter(item => item.product_id !== productId);\n        setCartItems(newCartItems);\n        localStorage.setItem('cart', JSON.stringify(newCartItems));\n      }\n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Failed to remove from cart' \n      };\n    }\n  };\n\n  const clearCart = async () => {\n    try {\n      if (isAuthenticated) {\n        await cartAPI.clear();\n      } else {\n        localStorage.removeItem('cart');\n      }\n      setCartItems([]);\n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Failed to clear cart' \n      };\n    }\n  };\n\n  const getCartTotal = () => {\n    return cartItems.reduce((total, item) => {\n      return total + (item.price * item.quantity);\n    }, 0);\n  };\n\n  const getCartItemsCount = () => {\n    return cartItems.reduce((total, item) => total + item.quantity, 0);\n  };\n\n  const value = {\n    cartItems,\n    loading,\n    addToCart,\n    updateCartItem,\n    removeFromCart,\n    clearCart,\n    getCartTotal,\n    getCartItemsCount,\n    loadCart,\n  };\n\n  return (\n    <CartContext.Provider value={value}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEkB;EAAgB,CAAC,GAAGf,OAAO,CAAC,CAAC;;EAErC;EACAF,SAAS,CAAC,MAAM;IACd,IAAI,CAACiB,eAAe,EAAE;MACpB,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC9C,IAAIF,SAAS,EAAE;QACb,IAAI;UACFJ,YAAY,CAACO,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdJ,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;QACjC;MACF;IACF;EACF,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;;EAErB;EACAjB,SAAS,CAAC,MAAM;IACd,IAAIiB,eAAe,EAAE;MACnBQ,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAErB,MAAMQ,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACR,eAAe,EAAE;IAEtB,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMU,QAAQ,GAAG,MAAMzB,OAAO,CAAC0B,GAAG,CAAC,CAAC;MACpCb,YAAY,CAACY,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAON,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,SAAS,GAAG,MAAAA,CAAOC,SAAS,EAAEC,QAAQ,GAAG,CAAC,KAAK;IACnD,IAAI;MACF,IAAIhB,eAAe,EAAE;QACnB,MAAMhB,OAAO,CAACiC,GAAG,CAACF,SAAS,EAAEC,QAAQ,CAAC;QACtC,MAAMR,QAAQ,CAAC,CAAC;MAClB,CAAC,MAAM;QACL;QACA,MAAMU,YAAY,GAAGtB,SAAS,CAACuB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,KAAKN,SAAS,CAAC;QAC1E,IAAIO,YAAY;QAEhB,IAAIJ,YAAY,EAAE;UAChBI,YAAY,GAAG1B,SAAS,CAAC2B,GAAG,CAACH,IAAI,IAC/BA,IAAI,CAACC,UAAU,KAAKN,SAAS,GACzB;YAAE,GAAGK,IAAI;YAAEJ,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,GAAGA;UAAS,CAAC,GAC/CI,IACN,CAAC;QACH,CAAC,MAAM;UACLE,YAAY,GAAG,CAAC,GAAG1B,SAAS,EAAE;YAAEyB,UAAU,EAAEN,SAAS;YAAEC;UAAS,CAAC,CAAC;QACpE;QAEAnB,YAAY,CAACyB,YAAY,CAAC;QAC1BpB,YAAY,CAACsB,OAAO,CAAC,MAAM,EAAEpB,IAAI,CAACqB,SAAS,CAACH,YAAY,CAAC,CAAC;MAC5D;MACA,OAAO;QAAEI,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAAqB,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLF,OAAO,EAAE,KAAK;QACdpB,KAAK,EAAE,EAAAqB,eAAA,GAAArB,KAAK,CAACG,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhB,IAAI,cAAAiB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAOf,SAAS,EAAEC,QAAQ,KAAK;IACpD,IAAI;MACF,IAAIhB,eAAe,EAAE;QACnB,MAAMhB,OAAO,CAAC+C,MAAM,CAAChB,SAAS,EAAEC,QAAQ,CAAC;QACzC,MAAMR,QAAQ,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAMc,YAAY,GAAG1B,SAAS,CAAC2B,GAAG,CAACH,IAAI,IACrCA,IAAI,CAACC,UAAU,KAAKN,SAAS,GACzB;UAAE,GAAGK,IAAI;UAAEJ;QAAS,CAAC,GACrBI,IACN,CAAC;QACDvB,YAAY,CAACyB,YAAY,CAAC;QAC1BpB,YAAY,CAACsB,OAAO,CAAC,MAAM,EAAEpB,IAAI,CAACqB,SAAS,CAACH,YAAY,CAAC,CAAC;MAC5D;MACA,OAAO;QAAEI,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdpB,KAAK,EAAE,EAAA0B,gBAAA,GAAA1B,KAAK,CAACG,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMK,cAAc,GAAG,MAAOnB,SAAS,IAAK;IAC1C,IAAI;MACF,IAAIf,eAAe,EAAE;QACnB,MAAMhB,OAAO,CAACmD,MAAM,CAACpB,SAAS,CAAC;QAC/B,MAAMP,QAAQ,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAMc,YAAY,GAAG1B,SAAS,CAACwC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,UAAU,KAAKN,SAAS,CAAC;QAC5ElB,YAAY,CAACyB,YAAY,CAAC;QAC1BpB,YAAY,CAACsB,OAAO,CAAC,MAAM,EAAEpB,IAAI,CAACqB,SAAS,CAACH,YAAY,CAAC,CAAC;MAC5D;MACA,OAAO;QAAEI,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLZ,OAAO,EAAE,KAAK;QACdpB,KAAK,EAAE,EAAA+B,gBAAA,GAAA/B,KAAK,CAACG,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMU,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,IAAIvC,eAAe,EAAE;QACnB,MAAMhB,OAAO,CAACwD,KAAK,CAAC,CAAC;MACvB,CAAC,MAAM;QACLtC,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;MACjC;MACAV,YAAY,CAAC,EAAE,CAAC;MAChB,OAAO;QAAE6B,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLhB,OAAO,EAAE,KAAK;QACdpB,KAAK,EAAE,EAAAmC,gBAAA,GAAAnC,KAAK,CAACG,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO/C,SAAS,CAACgD,MAAM,CAAC,CAACC,KAAK,EAAEzB,IAAI,KAAK;MACvC,OAAOyB,KAAK,GAAIzB,IAAI,CAAC0B,KAAK,GAAG1B,IAAI,CAACJ,QAAS;IAC7C,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,MAAM+B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOnD,SAAS,CAACgD,MAAM,CAAC,CAACC,KAAK,EAAEzB,IAAI,KAAKyB,KAAK,GAAGzB,IAAI,CAACJ,QAAQ,EAAE,CAAC,CAAC;EACpE,CAAC;EAED,MAAMgC,KAAK,GAAG;IACZpD,SAAS;IACTE,OAAO;IACPgB,SAAS;IACTgB,cAAc;IACdI,cAAc;IACdK,SAAS;IACTI,YAAY;IACZI,iBAAiB;IACjBvC;EACF,CAAC;EAED,oBACErB,OAAA,CAACC,WAAW,CAAC6D,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAtD,QAAA,EAChCA;EAAQ;IAAAwD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC1D,GAAA,CA9JWF,YAAY;EAAA,QAGKR,OAAO;AAAA;AAAAqE,EAAA,GAHxB7D,YAAY;AAAA,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}