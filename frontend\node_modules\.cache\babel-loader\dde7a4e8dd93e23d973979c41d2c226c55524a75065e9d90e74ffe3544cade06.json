{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\pages\\\\ProductDetails.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetails = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-8\",\n      children: \"Product Details\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600\",\n      children: [\"Product details for ID: \", id, \" coming soon...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetails, \"yQgCIz/jJfqV1l9s2yoba81MT5A=\", false, function () {\n  return [useParams];\n});\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["React", "useParams", "jsxDEV", "_jsxDEV", "ProductDetails", "_s", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/pages/ProductDetails.js"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\n\nconst ProductDetails = () => {\n  const { id } = useParams();\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Product Details</h1>\n      <p className=\"text-gray-600\">Product details for ID: {id} coming soon...</p>\n    </div>\n  );\n};\n\nexport default ProductDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAG,CAAC,GAAGL,SAAS,CAAC,CAAC;EAE1B,oBACEE,OAAA;IAAKI,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CL,OAAA;MAAII,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1ET,OAAA;MAAGI,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,0BAAwB,EAACF,EAAE,EAAC,iBAAe;IAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzE,CAAC;AAEV,CAAC;AAACP,EAAA,CATID,cAAc;EAAA,QACHH,SAAS;AAAA;AAAAY,EAAA,GADpBT,cAAc;AAWpB,eAAeA,cAAc;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}