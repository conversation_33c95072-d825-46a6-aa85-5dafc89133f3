<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    send_error_response('Method not allowed', 405);
}

$connection = get_db_connection();

// Get query parameters
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : null;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
$offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
$featured = isset($_GET['featured']) ? true : false;

// Build query
$where_conditions = [];
$params = [];

if ($category_id) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_id;
}

if ($search) {
    $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get products with category names and first image
$query = "
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.stock,
        p.created_at,
        c.name as category_name,
        pi.image_path
    FROM Products p
    LEFT JOIN Categories c ON p.category_id = c.id
    LEFT JOIN ProductImages pi ON p.id = pi.product_id
    $where_clause
    GROUP BY p.id
    ORDER BY p.created_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;

$result = execute_query($connection, $query, $params);
$products = fetch_all($result);

// Get total count for pagination
$count_query = "
    SELECT COUNT(DISTINCT p.id) as total
    FROM Products p
    LEFT JOIN Categories c ON p.category_id = c.id
    $where_clause
";

$count_params = array_slice($params, 0, -2); // Remove limit and offset
$count_result = execute_query($connection, $count_query, $count_params);
$total_count = fetch_single($count_result)['total'];

close_db_connection($connection);

send_success_response([
    'products' => $products,
    'total' => (int)$total_count,
    'limit' => $limit,
    'offset' => $offset
]);
?>
