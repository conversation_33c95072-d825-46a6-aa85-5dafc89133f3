<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_error_response('Method not allowed', 405);
}

// Require admin access
$user = require_admin();

$input = get_json_input();

// Validate required fields
$required_fields = ['name', 'description', 'price', 'category_id', 'stock'];
$missing_fields = validate_required_fields($input, $required_fields);

if (!empty($missing_fields)) {
    send_error_response('Missing required fields: ' . implode(', ', $missing_fields));
}

$name = sanitize_input($input['name']);
$description = sanitize_input($input['description']);
$price = (float)$input['price'];
$category_id = (int)$input['category_id'];
$stock = (int)$input['stock'];

// Validate price and stock
if ($price < 0) {
    send_error_response('Price must be non-negative');
}

if ($stock < 0) {
    send_error_response('Stock must be non-negative');
}

$connection = get_db_connection();

// Check if category exists
$query = "SELECT id FROM Categories WHERE id = ?";
$result = execute_query($connection, $query, [$category_id]);
if (!fetch_single($result)) {
    close_db_connection($connection);
    send_error_response('Category not found');
}

// Insert product
$query = "INSERT INTO Products (name, description, price, category_id, stock) VALUES (?, ?, ?, ?, ?)";
$result = execute_query($connection, $query, [$name, $description, $price, $category_id, $stock]);

if (!$result) {
    close_db_connection($connection);
    send_error_response('Failed to create product');
}

$product_id = get_last_insert_id($connection);

// Get the created product
$query = "
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.stock,
        p.created_at,
        c.name as category_name
    FROM Products p
    LEFT JOIN Categories c ON p.category_id = c.id
    WHERE p.id = ?
";

$result = execute_query($connection, $query, [$product_id]);
$product = fetch_single($result);

close_db_connection($connection);

send_success_response(['product' => $product], 'Product created successfully');
?>
