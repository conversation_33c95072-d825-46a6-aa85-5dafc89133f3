{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    region: 'west_bank',\n    city: '',\n    address: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    // Validate passwords match\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      setLoading(false);\n      return;\n    }\n\n    // Remove confirmPassword before sending\n    const {\n      confirmPassword,\n      ...registerData\n    } = formData;\n    const result = await register(registerData);\n    if (result.success) {\n      navigate('/');\n    } else {\n      setError(result.error);\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/logo.png\",\n            alt: \"KyoPal\",\n            className: \"w-16 h-16 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-red-600 hover:text-red-500\",\n            children: \"sign in to your existing account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"name\",\n              name: \"name\",\n              type: \"text\",\n              required: true,\n              value: formData.name,\n              onChange: handleChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Enter your full name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              autoComplete: \"email\",\n              required: true,\n              value: formData.email,\n              onChange: handleChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: \"password\",\n              autoComplete: \"new-password\",\n              required: true,\n              value: formData.password,\n              onChange: handleChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Enter your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"confirmPassword\",\n              name: \"confirmPassword\",\n              type: \"password\",\n              autoComplete: \"new-password\",\n              required: true,\n              value: formData.confirmPassword,\n              onChange: handleChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Confirm your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"phone\",\n              name: \"phone\",\n              type: \"tel\",\n              required: true,\n              value: formData.phone,\n              onChange: handleChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Enter your phone number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"region\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Region\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"region\",\n              name: \"region\",\n              required: true,\n              value: formData.region,\n              onChange: handleChange,\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"west_bank\",\n                children: \"West Bank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"occupied_territories\",\n                children: \"Occupied Territories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"city\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"City\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"city\",\n              name: \"city\",\n              type: \"text\",\n              required: true,\n              value: formData.city,\n              onChange: handleChange,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Enter your city\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"address\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"address\",\n              name: \"address\",\n              required: true,\n              value: formData.address,\n              onChange: handleChange,\n              rows: 3,\n              className: \"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\",\n              placeholder: \"Enter your full address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), \"Creating account...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this) : 'Create account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"epZUepMFwqQTRv1whtzRjERAB40=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Register", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "region", "city", "address", "loading", "setLoading", "error", "setError", "register", "navigate", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "registerData", "result", "success", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "id", "type", "required", "onChange", "placeholder", "autoComplete", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Register = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    region: 'west_bank',\n    city: '',\n    address: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { register } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    // Validate passwords match\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      setLoading(false);\n      return;\n    }\n\n    // Remove confirmPassword before sending\n    const { confirmPassword, ...registerData } = formData;\n\n    const result = await register(registerData);\n\n    if (result.success) {\n      navigate('/');\n    } else {\n      setError(result.error);\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"flex justify-center\">\n            <img src=\"/logo.png\" alt=\"KyoPal\" className=\"w-16 h-16 rounded-full\" />\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link to=\"/login\" className=\"font-medium text-red-600 hover:text-red-500\">\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Full Name\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Enter your full name\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                required\n                value={formData.password}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Enter your password\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                required\n                value={formData.confirmPassword}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Confirm your password\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n                Phone Number\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                required\n                value={formData.phone}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Enter your phone number\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"region\" className=\"block text-sm font-medium text-gray-700\">\n                Region\n              </label>\n              <select\n                id=\"region\"\n                name=\"region\"\n                required\n                value={formData.region}\n                onChange={handleChange}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n              >\n                <option value=\"west_bank\">West Bank</option>\n                <option value=\"occupied_territories\">Occupied Territories</option>\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"city\" className=\"block text-sm font-medium text-gray-700\">\n                City\n              </label>\n              <input\n                id=\"city\"\n                name=\"city\"\n                type=\"text\"\n                required\n                value={formData.city}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Enter your city\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"address\" className=\"block text-sm font-medium text-gray-700\">\n                Address\n              </label>\n              <textarea\n                id=\"address\"\n                name=\"address\"\n                required\n                value={formData.address}\n                onChange={handleChange}\n                rows={3}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm\"\n                placeholder=\"Enter your full address\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Creating account...\n                </div>\n              ) : (\n                'Create account'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEsB;EAAS,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,MAAMsB,YAAY,GAAIC,CAAC,IAAK;IAC1BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAAChB,IAAI,GAAGe,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIb,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDQ,QAAQ,CAAC,wBAAwB,CAAC;MAClCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,MAAM;MAAEN,eAAe;MAAE,GAAGiB;IAAa,CAAC,GAAGtB,QAAQ;IAErD,MAAMuB,MAAM,GAAG,MAAMT,QAAQ,CAACQ,YAAY,CAAC;IAE3C,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClBT,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,MAAM;MACLF,QAAQ,CAACU,MAAM,CAACX,KAAK,CAAC;IACxB;IAEAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEd,OAAA;IAAK4B,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClG7B,OAAA;MAAK4B,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC7B,OAAA;QAAA6B,QAAA,gBACE7B,OAAA;UAAK4B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC7B,OAAA;YAAK8B,GAAG,EAAC,WAAW;YAACC,GAAG,EAAC,QAAQ;YAACH,SAAS,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNnC,OAAA;UAAI4B,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnC,OAAA;UAAG4B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,IAClD,EAAC,GAAG,eACN7B,OAAA,CAACJ,IAAI;YAACwC,EAAE,EAAC,QAAQ;YAACR,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnC,OAAA;QAAM4B,SAAS,EAAC,gBAAgB;QAACS,QAAQ,EAAEd,YAAa;QAAAM,QAAA,GACrDd,KAAK,iBACJf,OAAA;UAAK4B,SAAS,EAAC,mEAAmE;UAAAC,QAAA,EAC/Ed;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,MAAM;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,MAAM;cACTlC,IAAI,EAAC,MAAM;cACXmC,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACE,IAAK;cACrBqC,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAsB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,OAAO;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,OAAO;cACVlC,IAAI,EAAC,OAAO;cACZmC,IAAI,EAAC,OAAO;cACZI,YAAY,EAAC,OAAO;cACpBH,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;cACtBoC,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAkB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,UAAU;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,UAAU;cACblC,IAAI,EAAC,UAAU;cACfmC,IAAI,EAAC,UAAU;cACfI,YAAY,EAAC,cAAc;cAC3BH,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACI,QAAS;cACzBmC,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAqB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,iBAAiB;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAErF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,iBAAiB;cACpBlC,IAAI,EAAC,iBAAiB;cACtBmC,IAAI,EAAC,UAAU;cACfI,YAAY,EAAC,cAAc;cAC3BH,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACK,eAAgB;cAChCkC,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAuB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,OAAO;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,OAAO;cACVlC,IAAI,EAAC,OAAO;cACZmC,IAAI,EAAC,KAAK;cACVC,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACM,KAAM;cACtBiC,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAyB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,QAAQ;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE5E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,QAAQ;cACXlC,IAAI,EAAC,QAAQ;cACboC,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACO,MAAO;cACvBgC,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,wJAAwJ;cAAAC,QAAA,gBAElK7B,OAAA;gBAAQsB,KAAK,EAAC,WAAW;gBAAAO,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CnC,OAAA;gBAAQsB,KAAK,EAAC,sBAAsB;gBAAAO,QAAA,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,MAAM;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,MAAM;cACTlC,IAAI,EAAC,MAAM;cACXmC,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACQ,IAAK;cACrB+B,QAAQ,EAAEvB,YAAa;cACvBS,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAiB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOsC,OAAO,EAAC,SAAS;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cACEuC,EAAE,EAAC,SAAS;cACZlC,IAAI,EAAC,SAAS;cACdoC,QAAQ;cACRnB,KAAK,EAAEnB,QAAQ,CAACS,OAAQ;cACxB8B,QAAQ,EAAEvB,YAAa;cACvB0B,IAAI,EAAE,CAAE;cACRjB,SAAS,EAAC,iMAAiM;cAC3Me,WAAW,EAAC;YAAyB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA;UAAA6B,QAAA,eACE7B,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbM,QAAQ,EAAEjC,OAAQ;YAClBe,SAAS,EAAC,4QAA4Q;YAAAC,QAAA,EAErRhB,OAAO,gBACNb,OAAA;cAAK4B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7B,OAAA;gBAAK4B,SAAS,EAAC;cAAgE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,uBAExF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CArOID,QAAQ;EAAA,QAcSH,OAAO,EACXD,WAAW;AAAA;AAAAkD,EAAA,GAfxB9C,QAAQ;AAuOd,eAAeA,QAAQ;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}