<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    send_error_response('Method not allowed', 405);
}

$user = require_admin();

$connection = get_db_connection();

// Get dashboard statistics
$stats = [];

// Total users
$query = "SELECT COUNT(*) as total FROM Users WHERE role = 'user'";
$result = execute_query($connection, $query);
$stats['total_users'] = fetch_single($result)['total'];

// Total products
$query = "SELECT COUNT(*) as total FROM Products";
$result = execute_query($connection, $query);
$stats['total_products'] = fetch_single($result)['total'];

// Total orders
$query = "SELECT COUNT(*) as total FROM Orders WHERE status != 'cart'";
$result = execute_query($connection, $query);
$stats['total_orders'] = fetch_single($result)['total'];

// Total revenue
$query = "SELECT SUM(total_amount) as total FROM Orders WHERE status = 'delivered'";
$result = execute_query($connection, $query);
$revenue = fetch_single($result)['total'];
$stats['total_revenue'] = $revenue ? format_price($revenue) : '0.00';

// Recent orders
$query = "
    SELECT 
        o.id,
        o.customer_name,
        o.total_amount,
        o.status,
        o.created_at
    FROM Orders o
    WHERE o.status != 'cart'
    ORDER BY o.created_at DESC
    LIMIT 10
";
$result = execute_query($connection, $query);
$recent_orders = fetch_all($result);

// Low stock products
$query = "
    SELECT 
        p.id,
        p.name,
        p.stock,
        c.name as category_name
    FROM Products p
    LEFT JOIN Categories c ON p.category_id = c.id
    WHERE p.stock <= 5
    ORDER BY p.stock ASC
    LIMIT 10
";
$result = execute_query($connection, $query);
$low_stock_products = fetch_all($result);

close_db_connection($connection);

send_success_response([
    'stats' => $stats,
    'recent_orders' => $recent_orders,
    'low_stock_products' => $low_stock_products
]);
?>
