language: node_js
node_js:
  - '12'
  - '11'
  - '10'
  - '9'
  - '8'
  - '6'
  - '4'
  - '0.12'
  - '0.10'
  - '0.8'
  - '0.6'
before_install:
  - 'nvm install-latest-npm'
install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ] || [ "${TRAVIS_NODE_VERSION}" = "0.9" ]; then nvm install --latest-npm 0.8 && npm install && nvm use "${TRAVIS_NODE_VERSION}"; else npm install; fi;'
sudo: false
matrix:
  fast_finish: true
  allow_failures:
    - node_js: "0.6"
