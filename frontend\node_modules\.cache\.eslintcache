[{"C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Cart.js": "3", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Products.js": "4", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Home.js": "6", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Register.js": "7", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\ProductDetails.js": "8", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Checkout.js": "9", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Orders.js": "10", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Account.js": "11", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Favorites.js": "12", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\About.js": "13", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Contact.js": "14", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Dashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Categories.js": "16", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Products.js": "17", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Users.js": "18", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Settings.js": "19", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Orders.js": "20", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\AdminRoute.js": "21", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\contexts\\CartContext.js": "22", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\contexts\\AuthContext.js": "23", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\ProtectedRoute.js": "24", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\Layout\\Footer.js": "25", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\Layout\\Header.js": "26", "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\services\\api.js": "27"}, {"size": 263, "mtime": 1749060952588, "results": "28", "hashOfConfig": "29"}, {"size": 3934, "mtime": 1749060923136, "results": "30", "hashOfConfig": "29"}, {"size": 294, "mtime": 1749061265004, "results": "31", "hashOfConfig": "29"}, {"size": 301, "mtime": 1749061252161, "results": "32", "hashOfConfig": "29"}, {"size": 5002, "mtime": 1749061242944, "results": "33", "hashOfConfig": "29"}, {"size": 12252, "mtime": 1749064295836, "results": "34", "hashOfConfig": "29"}, {"size": 305, "mtime": 1749061278458, "results": "35", "hashOfConfig": "29"}, {"size": 412, "mtime": 1749061259502, "results": "36", "hashOfConfig": "29"}, {"size": 301, "mtime": 1749061272240, "results": "37", "hashOfConfig": "29"}, {"size": 296, "mtime": 1749061290944, "results": "38", "hashOfConfig": "29"}, {"size": 300, "mtime": 1749061284458, "results": "39", "hashOfConfig": "29"}, {"size": 308, "mtime": 1749061298449, "results": "40", "hashOfConfig": "29"}, {"size": 292, "mtime": 1749061305870, "results": "41", "hashOfConfig": "29"}, {"size": 300, "mtime": 1749061313250, "results": "42", "hashOfConfig": "29"}, {"size": 312, "mtime": 1749061319470, "results": "43", "hashOfConfig": "29"}, {"size": 328, "mtime": 1749061352784, "results": "44", "hashOfConfig": "29"}, {"size": 320, "mtime": 1749061326633, "results": "45", "hashOfConfig": "29"}, {"size": 308, "mtime": 1749061346994, "results": "46", "hashOfConfig": "29"}, {"size": 302, "mtime": 1749061358930, "results": "47", "hashOfConfig": "29"}, {"size": 312, "mtime": 1749061333526, "results": "48", "hashOfConfig": "29"}, {"size": 793, "mtime": 1749061170217, "results": "49", "hashOfConfig": "29"}, {"size": 4586, "mtime": 1749061925167, "results": "50", "hashOfConfig": "29"}, {"size": 3053, "mtime": 1749061024102, "results": "51", "hashOfConfig": "29"}, {"size": 687, "mtime": 1749061162894, "results": "52", "hashOfConfig": "29"}, {"size": 5734, "mtime": 1749063929758, "results": "53", "hashOfConfig": "29"}, {"size": 19373, "mtime": 1749061114555, "results": "54", "hashOfConfig": "29"}, {"size": 3641, "mtime": 1749062360411, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9tj5qu", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Cart.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Products.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\ProductDetails.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Checkout.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Orders.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Account.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Favorites.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Categories.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Products.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Users.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Settings.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\pages\\Admin\\Orders.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\AdminRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\contexts\\CartContext.js", [], ["137"], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\Layout\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\KyoPal\\frontend\\src\\services\\api.js", [], [], {"ruleId": "138", "severity": 1, "message": "139", "line": 39, "column": 6, "nodeType": "140", "endLine": 39, "endColumn": 23, "suggestions": "141", "suppressions": "142"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCart'. Either include it or remove the dependency array.", "ArrayExpression", ["143"], ["144"], {"desc": "145", "fix": "146"}, {"kind": "147", "justification": "148"}, "Update the dependencies array to be: [isAuthenticated, loadCart]", {"range": "149", "text": "150"}, "directive", "", [1069, 1086], "[isAuthenticated, loadCart]"]