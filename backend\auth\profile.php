<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get user profile
    $user = require_auth();
    
    $connection = get_db_connection();
    $query = "SELECT id, name, email, phone, region, city, address, role, created_at FROM Users WHERE id = ?";
    $result = execute_query($connection, $query, [$user['id']]);
    $profile = fetch_single($result);
    close_db_connection($connection);
    
    if (!$profile) {
        send_error_response('User not found', 404);
    }
    
    send_success_response(['user' => $profile]);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    // Update user profile
    $user = require_auth();
    $input = get_json_input();
    
    $connection = get_db_connection();
    
    // Build update query dynamically
    $update_fields = [];
    $params = [];
    
    if (isset($input['name']) && !empty(trim($input['name']))) {
        $update_fields[] = "name = ?";
        $params[] = sanitize_input($input['name']);
    }
    
    if (isset($input['phone']) && !empty(trim($input['phone']))) {
        $update_fields[] = "phone = ?";
        $params[] = sanitize_input($input['phone']);
    }
    
    if (isset($input['region']) && in_array($input['region'], ['west_bank', 'occupied_territories'])) {
        $update_fields[] = "region = ?";
        $params[] = $input['region'];
    }
    
    if (isset($input['city']) && !empty(trim($input['city']))) {
        $update_fields[] = "city = ?";
        $params[] = sanitize_input($input['city']);
    }
    
    if (isset($input['address']) && !empty(trim($input['address']))) {
        $update_fields[] = "address = ?";
        $params[] = sanitize_input($input['address']);
    }
    
    if (empty($update_fields)) {
        close_db_connection($connection);
        send_error_response('No valid fields to update');
    }
    
    $params[] = $user['id'];
    $query = "UPDATE Users SET " . implode(', ', $update_fields) . " WHERE id = ?";
    $result = execute_query($connection, $query, $params);
    
    if (!$result) {
        close_db_connection($connection);
        send_error_response('Failed to update profile');
    }
    
    // Get updated user data
    $query = "SELECT id, name, email, phone, region, city, address, role FROM Users WHERE id = ?";
    $result = execute_query($connection, $query, [$user['id']]);
    $updated_user = fetch_single($result);
    close_db_connection($connection);
    
    send_success_response(['user' => $updated_user], 'Profile updated successfully');
    
} else {
    send_error_response('Method not allowed', 405);
}
?>
