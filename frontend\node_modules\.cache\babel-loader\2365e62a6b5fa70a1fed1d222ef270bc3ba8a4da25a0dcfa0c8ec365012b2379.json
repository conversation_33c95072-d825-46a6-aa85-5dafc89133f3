{"ast": null, "code": "'use strict';\n\nexport default function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n}", "map": {"version": 3, "names": ["isCancel", "value", "__CANCEL__"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/node_modules/axios/lib/cancel/isCancel.js"], "sourcesContent": ["'use strict';\n\nexport default function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,eAAe,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}