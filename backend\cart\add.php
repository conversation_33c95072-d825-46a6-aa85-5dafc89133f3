<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_error_response('Method not allowed', 405);
}

$user = require_auth();
$input = get_json_input();

// Validate required fields
$required_fields = ['product_id'];
$missing_fields = validate_required_fields($input, $required_fields);

if (!empty($missing_fields)) {
    send_error_response('Missing required fields: ' . implode(', ', $missing_fields));
}

$product_id = (int)$input['product_id'];
$quantity = isset($input['quantity']) ? (int)$input['quantity'] : 1;

if ($quantity <= 0) {
    send_error_response('Quantity must be positive');
}

$connection = get_db_connection();

// Check if product exists and get its details
$query = "SELECT id, name, price, stock FROM Products WHERE id = ?";
$result = execute_query($connection, $query, [$product_id]);
$product = fetch_single($result);

if (!$product) {
    close_db_connection($connection);
    send_error_response('Product not found', 404);
}

if ($product['stock'] < $quantity) {
    close_db_connection($connection);
    send_error_response('Insufficient stock');
}

// Get or create cart order
$query = "SELECT id FROM Orders WHERE user_id = ? AND status = 'cart'";
$result = execute_query($connection, $query, [$user['id']]);
$cart_order = fetch_single($result);

if (!$cart_order) {
    // Create new cart order
    $query = "INSERT INTO Orders (user_id, status) VALUES (?, 'cart')";
    $result = execute_query($connection, $query, [$user['id']]);
    if (!$result) {
        close_db_connection($connection);
        send_error_response('Failed to create cart');
    }
    $order_id = get_last_insert_id($connection);
} else {
    $order_id = $cart_order['id'];
}

// Check if item already exists in cart
$query = "SELECT id, quantity FROM OrderItems WHERE order_id = ? AND product_id = ?";
$result = execute_query($connection, $query, [$order_id, $product_id]);
$existing_item = fetch_single($result);

if ($existing_item) {
    // Update existing item
    $new_quantity = $existing_item['quantity'] + $quantity;
    if ($product['stock'] < $new_quantity) {
        close_db_connection($connection);
        send_error_response('Insufficient stock');
    }
    
    $query = "UPDATE OrderItems SET quantity = ? WHERE id = ?";
    $result = execute_query($connection, $query, [$new_quantity, $existing_item['id']]);
} else {
    // Add new item
    $query = "INSERT INTO OrderItems (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)";
    $result = execute_query($connection, $query, [$order_id, $product_id, $quantity, $product['price']]);
}

if (!$result) {
    close_db_connection($connection);
    send_error_response('Failed to add item to cart');
}

close_db_connection($connection);

send_success_response([], 'Item added to cart successfully');
?>
