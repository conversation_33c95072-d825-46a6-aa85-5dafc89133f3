<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Website' ?></title>
    <link rel="icon" href="<?= $logo_path ?>?v=1">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merienda:wght@300..900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Merienda", cursive;
            font-optical-sizing: auto;
            font-style: normal;
            background-color: white;
            color: #111827;
        }
        .footer-link {
            color: gray;
            transition: color 0.3s ease;
        }
        .footer-link:hover {
            color: #dc2626;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        @media (max-width: 640px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body class="font-merienda min-h-screen flex flex-col">
<header class="sticky top-0 z-50 w-full shadow-md bg-white backdrop-blur supports-[backdrop-filter]:bg-white/80">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
            <div class="flex items-center">
                <a href="/index.php" class="flex items-center space-x-2">
                    <img src="<?= $logo_path ?>" alt="<?= $store_name ?>" class="w-10 h-10 rounded-full" />
                    <span class="inline-block text-xl font-bold text-black"><?= $store_name ?></span>
                </a>
            </div>
            <nav class="hidden lg:flex space-x-6">
                <a href="/index.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($current_page == 'index') echo 'text-red-600 hover:text-red-800'; ?>">Home</a>
                <a href="/products.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($current_page == 'products') echo 'text-red-600 hover:text-red-800'; ?>">Products</a>
                <a href="/about.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($current_page == 'about') echo 'text-red-600 hover:text-red-800'; ?>">About</a>
                <a href="/contact.php" class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors <?php if ($current_page == 'contact') echo 'text-red-600 hover:text-red-800'; ?>">Contact</a>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="relative hidden sm:block">
                    <form action="/products.php" method="GET">
                        <input type="text" name="search" placeholder="Search..." value="<?= isset($_GET['search']) ? htmlspecialchars($_GET['search']) : '' ?>"
                            class="w-64 px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300" />
                        <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </form>
                </div>
                <button id="mobileSearchBtn" class="sm:hidden text-gray-600 hover:text-red-600 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
                <a href="/cart.php" class="relative group">
                    <svg xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6 text-gray-600 group-hover:text-red-600 transition-colors" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span id="cart-count"
                        class="absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center group-hover:bg-red-700 transition-colors"><?= $is_logged_in ? $cart_count : 0 ?></span>
                </a>

                <?php if (!$is_logged_in): ?>
                <!-- Login/Signup buttons for non-logged in users -->
                <div class="hidden sm:flex items-center space-x-2">
                    <a href="/login.php"
                        class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50">
                        Login
                    </a>
                    <a href="/register.php"
                        class="text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md">
                        Sign Up
                    </a>
                </div>
                <?php else: ?>
                <!-- Profile dropdown for logged in users -->
                <div class="relative">
                    <button id="profileDropdownBtn"
                        class="text-gray-600 flex items-center hover:text-red-600 transition-colors focus:outline-none cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <svg id="arrowIcon" xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            class="h-3 w-3 transition-transform duration-300"
                            viewBox="0 0 330 330">
                            <path id="XMLID_225_"
                                d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393  c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393  s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z" />
                        </svg>
                    </button>
                    <div id="profileDropdown"
                        class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg py-2 z-50 hidden transform transition-transform duration-200 origin-top-right">
                        <div class="px-4 py-3 border-b border-gray-200">
                            <p class="text-sm leading-5 font-medium text-gray-900"><?= htmlspecialchars($current_user['name']) ?></p>
                            <p class="text-xs leading-4 font-normal text-gray-500 mt-1"><?= htmlspecialchars($current_user['email']) ?></p>
                        </div>
                        <?php if ($current_user['role'] === 'admin'): ?>
                        <a href="/admin/index.php"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                                </svg>
                                Admin Dashboard
                            </div>
                        </a>
                        <?php endif; ?>
                        <a href="/account.php" id="profile"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile
                            </div>
                        </a>
                        <a href="/orders.php"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                </svg>
                                My Orders
                            </div>
                        </a>
                        <a href="/favorites.php"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                                Favorites
                            </div>
                        </a>
                        <div class="border-t border-gray-200 my-1"></div>
                        <a href="/logout.php"
                            class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </div>
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <button id="mobileMenuBtn"
                    class="lg:hidden text-gray-600 hover:text-red-600 rounded-full cursor-pointer transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
    <div id="mobileMenu"
        class="max-h-0 opacity-0 lg:hidden border-t border-gray-200 rounded-b-xl overflow-hidden transition-all duration-300 ease-in-out">
        <div class="px-6">
            <nav class="flex flex-col divide-y divide-gray-200">
                <a href="/index.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($current_page == 'index') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                        <path
                            d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                    </svg>
                    <span>Home</span>
                </a>
                <a href="/products.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($current_page == 'products') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <path d="m7.5 4.27 9 5.15"></path>
                        <path
                            d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z">
                        </path>
                        <path d="m3.3 7 8.7 5 8.7-5"></path>
                        <path d="M12 22V12"></path>
                    </svg>
                    <span>Products</span>
                </a>
                <a href="/about.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($current_page == 'about') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 16v-4"></path>
                        <path d="M12 8h.01"></path>
                    </svg>
                    <span>About</span>
                </a>
                <a href="/contact.php" class="group flex items-center gap-4 py-4 text-md font-semibold text-gray-700 hover:text-red-600 transition-all duration-300 <?php if ($current_page == 'contact') echo 'text-red-600 hover:text-red-800'; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="w-5 h-5 transition duration-300">
                        <path
                            d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                        </path>
                    </svg>
                    <span>Contact</span>
                </a>

                <?php if (!$is_logged_in): ?>
                <div class="flex flex-col gap-2 py-4">
                    <a href="/login.php"
                        class="text-center text-sm font-medium text-gray-600 hover:text-red-600 transition-colors px-4 py-2 rounded-md hover:bg-gray-50">
                        Login
                    </a>
                    <a href="/register.php"
                        class="text-center text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors px-4 py-2 rounded-md">
                        Sign Up
                    </a>
                </div>
                <?php endif; ?>
            </nav>
        </div>
    </div>
    <div id="mobileSearch"
        class="max-h-0 opacity-0 sm:hidden border-t border-gray-200 transition-all duration-300 ease-in-out overflow-hidden">
        <div class="mx-auto px-4 py-3">
            <div class="relative">
                <form action="/products.php" method="GET">
                    <input type="text" name="search" placeholder="Search..." value="<?= isset($_GET['search']) ? htmlspecialchars($_GET['search']) : '' ?>"
                        class="w-full px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-red-600 focus:bg-white transition-all duration-300" />
                    <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>
</header>
<script>
    const profileBtn = document.getElementById('profileDropdownBtn');
    const profileDropdown = document.getElementById('profileDropdown');
    const arrow = document.getElementById('arrowIcon');
    let isOpen = false;

    if (profileBtn) {
        profileBtn.addEventListener('click', function () {
            isOpen = !isOpen;
            profileDropdown.classList.toggle('hidden');
            arrow.classList.toggle('rotate-180', isOpen);
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!profileBtn.contains(event.target) && !profileDropdown.contains(event.target)) {
                profileDropdown.classList.add('hidden');
                arrow.classList.remove('rotate-180');
                isOpen = false;
            }
        });
    }

    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            if (mobileMenu.classList.contains('max-h-0')) {
                mobileMenu.classList.remove('max-h-0', 'opacity-0');
                mobileMenu.classList.add('max-h-96', 'opacity-100');
            } else {
                mobileMenu.classList.add('max-h-0', 'opacity-0');
                mobileMenu.classList.remove('max-h-96', 'opacity-100');
            }
        });
    }

    // Mobile search functionality
    const mobileSearchBtn = document.getElementById('mobileSearchBtn');
    const mobileSearch = document.getElementById('mobileSearch');

    if (mobileSearchBtn) {
        mobileSearchBtn.addEventListener('click', function() {
            if (mobileSearch.classList.contains('max-h-0')) {
                mobileSearch.classList.remove('max-h-0', 'opacity-0');
                mobileSearch.classList.add('max-h-20', 'opacity-100');
            } else {
                mobileSearch.classList.add('max-h-0', 'opacity-0');
                mobileSearch.classList.remove('max-h-20', 'opacity-100');
            }
        });
    }

    // Cart management for non-logged in users
    // Update cart count from localStorage
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = totalItems;
        }
    }

    // Initialize cart count on page load
    updateCartCount();

    // Listen for cart updates
    window.addEventListener('storage', updateCartCount);

    // Transfer cart from localStorage to database
    document.addEventListener('DOMContentLoaded', function() {
        const cart = localStorage.getItem('cart');
        if (cart && cart !== '[]') {
            fetch('transfer-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'cart=' + encodeURIComponent(cart)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    localStorage.removeItem('cart');
                    location.reload();
                }
            });
        }
    });
</script>
<main class="flex-1">

<section class="relative w-full min-h-[400px] py-10 md:py-14 overflow-hidden">
    <img src="images/website/background.jpg" class="absolute inset-0 w-full h-full object-cover opacity-5 z-0" alt="background">

    <div class="container relative z-10 text-center md:text-left space-y-6 md:space-y-10">
        <h1 class="text-red-600 text-md md:text-2xl font-bold">
            FIRST ANIME STORE IN PALESTINE!
        </h1>
        <p class="text-black font-extrabold text-3xl md:text-4xl">Discover World of <span class="text-red-600">Anime</span> Collectibles</p>
        <p class="text-black/70 text-lg md:w-2/3">
            Explore our premium selection of anime merchandise, figures, and collectibles. Find your favorite characters and series.
        </p>
        <div class="flex flex-wrap justify-center md:justify-start gap-4">
            <a href="products.php" class="bg-red-600 px-8 py-3 text-white font-bold rounded-lg text-md hover:bg-red-500 transition-transform shadow-md">
                Shop Now
            </a>
        </div>
    </div>
</section>

<?php if (!empty($categories)): ?>
<section class="container w-full text-center pt-12">
    <p class="text-3xl font-extrabold">Shop by Category</p>
    <p class="text-gray-700 mt-4">Explore our wide range of anime merchandise categories</p>
    <section class="mt-8 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pb-4">
        <?php foreach ($categories as $category): ?>
            <a href="products.php?category=<?= $category['id'] ?>" class="group">
                <article class="bg-black bg-opacity-80 shadow-md rounded-xl p-4 text-left h-40 bg-cover bg-center w-full mb-4 group-hover:bg-opacity-70 transition-all duration-300"
                    style="background-image: url('<?= $category['image_path'] ?: 'images/website/Kyo2.jpg' ?>');">
                    <div class="relative top-20">
                        <p class="text-lg font-extrabold text-white"><?= htmlspecialchars($category['name']) ?></p>
                        <p class="text-sm text-white mt-2"><?= $category['product_count'] ?> products</p>
                    </div>
                </article>
            </a>
        <?php endforeach; ?>
    </section>
</section>
<?php endif; ?>

<?php if (!empty($products)): ?>
<section class="container pt-12">
    <div class="text-center space-y-2">
        <p class="inline-block bg-red-600 text-white text-sm font-semibold px-4 py-1 rounded-full shadow-sm">
            Trending Now
        </p>
        <p class="text-3xl font-extrabold text-gray-800">
            Featured Products
        </p>
    </div>

    <section class="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <?php foreach ($products as $product): ?>
            <article class="group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto">
                <div class="overflow-hidden">
                    <img src="<?= $product['image_path'] ?: 'images/website/Kyo2.jpg' ?>" alt="<?= htmlspecialchars($product['name']) ?>"
                        class="object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105">
                </div>
                <div class="p-4 flex flex-col justify-between flex-1">
                    <div>
                        <p class="text-xs text-red-500 uppercase font-semibold"><?= htmlspecialchars($product['category_name'] ?: 'Uncategorized') ?></p>
                        <p class="text-sm font-bold text-gray-800 truncate"><?= htmlspecialchars($product['name']) ?></p>
                        <p class="text-sm text-gray-600">
                            $<?= number_format($product['price'], 2) ?>
                        </p>
                    </div>
                    <div class="mt-4 flex gap-2">
                        <button onclick="addToCart(<?= $product['id'] ?>)"
                            class="flex-1 bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200">
                            Add to Cart
                        </button>
                        <a href="product-details.php?id=<?= $product['id'] ?>"
                            class="px-3 py-2 border border-red-600 text-red-600 rounded-lg text-sm hover:bg-red-50 transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </a>
                    </div>
                </div>
            </article>
        <?php endforeach; ?>
    </section>
    <div class="flex items-center justify-center mt-12">
        <a href="products.php" class="flex items-center bg-white text-black rounded-md text-sm py-[10px] px-[20px] border border-gray-200 hover:bg-red-50 transition-transform duration-300 ease-in-out">
            View All Products
            <svg class="w-5 h-5 ml-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none"><path stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 12-6-6m6 6-6 6m6-6H5"/></svg>
        </a>
    </div>
</section>
<?php endif; ?>

<section class="py-12 bg-red-600 text-white mt-20">
    <div class="container">
        <div class="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div class="space-y-4">
                <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Special Offers</h2>
                <p class="max-w-[600px] md:text-xl">
                    Limited time offers on selected anime merchandise. Don't miss out on these exclusive deals!
                </p>
                <div class="flex flex-col gap-2 min-[400px]:flex-row">
                    <a href="products.php?sale=true" class="inline-block">
                        <button class="w-full min-[400px]:w-auto bg-white font-bold text-gray-800 rounded-lg hover:bg-white/90 py-[11px] px-[30px]">
                            Shop Sale
                        </button>
                    </a>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white text-red-600 rounded-lg shadow-md">
                    <div class="p-6 flex flex-col items-center text-center space-y-2">
                        <svg class="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path></svg>
                        <h3 class="font-bold">20% OFF</h3>
                        <p class="text-sm">On selected figures</p>
                    </div>
                </div>
                <div class="bg-white text-red-600 rounded-lg shadow-md">
                    <div class="p-6 flex flex-col items-center text-center space-y-2">
                        <svg class="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"></path></svg>
                        <h3 class="font-bold">FREE SHIPPING</h3>
                        <p class="text-sm">On orders over $50</p>
                    </div>
                </div>
                <div class="bg-white text-red-600 rounded-lg shadow-md">
                    <div class="p-6 flex flex-col items-center text-center space-y-2">
                        <svg class="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path></svg>
                        <h3 class="font-bold">GUARANTEE</h3>
                        <p class="text-sm">30-day money back</p>
                    </div>
                </div>
                <div class="bg-white text-red-600 rounded-lg shadow-md">
                    <div class="p-6 flex flex-col items-center text-center space-y-2">
                        <svg class="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path></svg>
                        <h3 class="font-bold">REWARDS</h3>
                        <p class="text-sm">Earn points with every purchase</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function addToCart(productId) {
        fetch('cart-handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=add&product_id=' + productId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error adding to cart');
            }
        });
        let cart = JSON.parse(localStorage.getItem('cart') || '[]');
        let existingItem = cart.find(item => item.id == productId);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({id: productId, quantity: 1});
        }

        localStorage.setItem('cart', JSON.stringify(cart));

        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCountElement.textContent = totalItems;
        }
}
</script>
</main>
<footer class="bg-red-50 pt-12 pb-8 border-t border-gray-200">
    <div class="container">
        <div class="grid gap-y-8 gap-x-8 md:gap-x-24 lg:gap-x-48 md:grid-cols-2 lg:grid-cols-4">
            <div class="space-y-4">
                <div class="flex items-center gap-2">
                    <img src="<?= $logo_path ?>" alt="<?= $store_name ?>" width="50" height="50" class="rounded-full" />
                    <h2 class="text-2xl font-bold text-gray-900"><?= $store_name ?></h2>
                </div>
                <p class="text-gray-500">Your ultimate destination for premium anime merchandise and collectibles.</p>
                <div class="flex gap-4">
                    <?php if ($facebook_url && $facebook_url !== '#'): ?>
                    <a href="<?= htmlspecialchars($facebook_url) ?>" target="_blank" class="footer-link" title="Facebook">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-facebook h-5 w-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
                        <span class="sr-only">Facebook</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($instagram_url && $instagram_url !== '#'): ?>
                    <a href="<?= htmlspecialchars($instagram_url) ?>" target="_blank" class="footer-link" title="Instagram">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-instagram h-5 w-5"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>
                        <span class="sr-only">Instagram</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($tiktok_url && $tiktok_url !== '#'): ?>
                    <a href="<?= htmlspecialchars($tiktok_url) ?>" target="_blank" class="footer-link" title="TikTok">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M9 12a4 4 0 1 0 4 4V4a5 5 0 0 0 5 5"></path></svg>
                        <span class="sr-only">TikTok</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($whatsapp_url && $whatsapp_url !== '#'): ?>
                    <a href="<?= htmlspecialchars($whatsapp_url) ?>" target="_blank" class="footer-link" title="WhatsApp">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21"></path><path d="M9 10a3 3 0 0 0 6 0"></path></svg>
                        <span class="sr-only">WhatsApp</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($telegram_url && $telegram_url !== '#'): ?>
                    <a href="<?= htmlspecialchars($telegram_url) ?>" target="_blank" class="footer-link" title="Telegram">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M22 2L11 13"></path><path d="M22 2l-7 20-4-9-9-4z"></path></svg>
                        <span class="sr-only">Telegram</span>
                    </a>
                    <?php endif; ?>

                    <?php if ($x_url && $x_url !== '#'): ?>
                    <a href="<?= htmlspecialchars($x_url) ?>" target="_blank" class="footer-link" title="X (Twitter)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M4 4l11.733 16h4.267l-11.733 -16z"></path><path d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772"></path></svg>
                        <span class="sr-only">X (Twitter)</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <div>
                <h3 class="font-bold text-lg mb-4 text-gray-900">Shop</h3>
                <ul class="space-y-2">
                    <li><a href="/products.php" class="footer-link">All Products</a></li>
                    <li><a href="/products.php?category=1" class="footer-link">Anime Figures</a></li>
                    <li><a href="/products.php?category=2" class="footer-link">Manga Books</a></li>
                    <li><a href="/products.php?category=3" class="footer-link">Dakimakura</a></li>
                    <li><a href="/products.php?category=4" class="footer-link">Keychains</a></li>
                    <li><a href="/products.php?category=6" class="footer-link">Cosplay</a></li>
                </ul>
            </div>

            <div>
                <h3 class="font-bold text-lg mb-4 text-gray-900">Customer Service</h3>
                <ul class="space-y-2">
                    <li><a href="/contact.php" class="footer-link">Contact Us</a></li>
                    <li><a href="/faq.php" class="footer-link">FAQ</a></li>
                    <li><a href="/terms.php" class="footer-link">Terms & Conditions</a></li>
                    <li><a href="/privacy.php" class="footer-link">Privacy Policy</a></li>
                    <li><a href="/about.php" class="footer-link">About Us</a></li>
                </ul>
            </div>

            <div>
                <h3 class="font-bold text-lg mb-4 text-gray-900">Contact Info</h3>
                <ul class="space-y-2 text-gray-500">
                    <li class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                        <span><?= htmlspecialchars($store_email) ?></span>
                    </li>
                    <li class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                        <span><?= htmlspecialchars($store_phone) ?></span>
                    </li>
                    <li class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                        <span><?= htmlspecialchars($store_location) ?></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-center text-gray-500">&copy;<?= date('Y') ?> <?= $store_name ?>. All rights reserved.</p>
        </div>
    </div>
</footer>
<script>
    // Mobile menu functionality is now handled in header.php
    // This script is kept for any additional footer-specific functionality

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
</script>
</body>
</html>