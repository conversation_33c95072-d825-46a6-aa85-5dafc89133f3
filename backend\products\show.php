<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    send_error_response('Method not allowed', 405);
}

$product_id = isset($_GET['id']) ? (int)$_GET['id'] : null;

if (!$product_id) {
    send_error_response('Product ID is required');
}

$connection = get_db_connection();

// Get product details
$query = "
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.stock,
        p.created_at,
        c.name as category_name,
        c.id as category_id
    FROM Products p
    LEFT JOIN Categories c ON p.category_id = c.id
    WHERE p.id = ?
";

$result = execute_query($connection, $query, [$product_id]);
$product = fetch_single($result);

if (!$product) {
    close_db_connection($connection);
    send_error_response('Product not found', 404);
}

// Get product images
$query = "SELECT image_path FROM ProductImages WHERE product_id = ? ORDER BY id";
$result = execute_query($connection, $query, [$product_id]);
$images = fetch_all($result);

$product['images'] = array_column($images, 'image_path');

close_db_connection($connection);

send_success_response(['product' => $product]);
?>
