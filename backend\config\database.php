<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'kyopal');

// Create database connection using mysqli procedural style
function get_db_connection() {
    $connection = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if (!$connection) {
        die("Connection failed: " . mysqli_connect_error());
    }
    
    // Set charset to utf8mb4
    mysqli_set_charset($connection, "utf8mb4");
    
    return $connection;
}

// Close database connection
function close_db_connection($connection) {
    if ($connection) {
        mysqli_close($connection);
    }
}

// Execute query and return result
function execute_query($connection, $query, $params = []) {
    if (!empty($params)) {
        $stmt = mysqli_prepare($connection, $query);
        if ($stmt) {
            $types = str_repeat('s', count($params)); // Assume all strings for simplicity
            mysqli_stmt_bind_param($stmt, $types, ...$params);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            mysqli_stmt_close($stmt);
            return $result;
        }
    } else {
        return mysqli_query($connection, $query);
    }
    return false;
}

// Get single row from result
function fetch_single($result) {
    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }
    return null;
}

// Get all rows from result
function fetch_all($result) {
    if ($result) {
        return mysqli_fetch_all($result, MYSQLI_ASSOC);
    }
    return [];
}

// Get last inserted ID
function get_last_insert_id($connection) {
    return mysqli_insert_id($connection);
}

// Escape string for SQL
function escape_string($connection, $string) {
    return mysqli_real_escape_string($connection, $string);
}
?>
