{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KyoPal\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { productsAPI, categoriesAPI } from '../services/api';\nimport { useCart } from '../contexts/CartContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const {\n    addToCart\n  } = useCart();\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [productsResponse, categoriesResponse] = await Promise.all([productsAPI.getAll({\n        limit: 8\n      }), categoriesAPI.getAll()]);\n      setFeaturedProducts(productsResponse.data.products || []);\n      setCategories(categoriesResponse.data.categories || []);\n    } catch (error) {\n      console.error('Failed to load data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddToCart = async productId => {\n    const result = await addToCart(productId);\n    if (result.success) {\n      // You could add a toast notification here\n      console.log('Product added to cart');\n    } else {\n      console.error('Failed to add to cart:', result.error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative w-full min-h-[400px] py-10 md:py-14 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/background.jpg\",\n        className: \"absolute inset-0 w-full h-full object-cover opacity-5 z-0\",\n        alt: \"background\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container relative z-10 text-center md:text-left space-y-6 md:space-y-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-red-600 text-md md:text-2xl font-bold\",\n          children: \"FIRST ANIME STORE IN PALESTINE!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-black font-extrabold text-3xl md:text-4xl\",\n          children: [\"Discover World of \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-600\",\n            children: \"Anime\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 31\n          }, this), \" Collectibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-black/70 text-lg md:w-2/3\",\n          children: \"Explore our premium selection of anime merchandise, figures, and collectibles. Find your favorite characters and series.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center md:justify-start gap-4\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"bg-red-600 px-8 py-3 text-white font-bold rounded-lg text-md hover:bg-red-500 transition-transform shadow-md\",\n            children: \"Shop Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), categories.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"container w-full text-center pt-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-extrabold\",\n        children: \"Shop by Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-700 mt-4\",\n        children: \"Explore our wide range of anime merchandise categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-8 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pb-4\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/products?category=${category.id}`,\n          className: \"group\",\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-black bg-opacity-80 shadow-md rounded-xl p-4 text-left h-40 bg-cover bg-center w-full mb-4 group-hover:bg-opacity-70 transition-all duration-300\",\n            style: {\n              backgroundImage: `url('${category.image || '/images/website/Kyo2.jpg'}')`\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative top-20\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-extrabold text-white\",\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-white mt-2\",\n                children: [category.product_count || 0, \" products\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 17\n          }, this)\n        }, category.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), featuredProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"container pt-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"inline-block bg-red-600 text-white text-sm font-semibold px-4 py-1 rounded-full shadow-sm\",\n          children: \"Trending Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-3xl font-extrabold text-gray-800\",\n          children: \"Featured Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(\"article\", {\n          className: \"group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.image_path || '/images/website/Kyo2.jpg',\n              alt: product.name,\n              className: \"object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 flex flex-col justify-between flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-500 uppercase font-semibold\",\n                children: product.category_name || 'Uncategorized'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-bold text-gray-800 truncate\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"$\", Number(product.price).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAddToCart(product.id),\n                className: \"flex-1 bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200\",\n                children: \"Add to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/product/${product.id}`,\n                className: \"px-3 py-2 border border-red-600 text-red-600 rounded-lg text-sm hover:bg-red-50 transition-colors duration-200 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-4 w-4\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mt-12\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          className: \"flex items-center bg-white text-black rounded-md text-sm py-[10px] px-[20px] border border-gray-200 hover:bg-red-50 transition-transform duration-300 ease-in-out\",\n          children: [\"View All Products\", /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 ml-2\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              stroke: \"#000000\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"m19 12-6-6m6 6-6 6m6-6H5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12 bg-red-600 text-white mt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid gap-6 lg:grid-cols-2 lg:gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\",\n              children: \"Special Offers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"max-w-[600px] md:text-xl\",\n              children: \"Limited time offers on selected anime merchandise. Don't miss out on these exclusive deals!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-2 min-[400px]:flex-row\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?sale=true\",\n                className: \"inline-block\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full min-[400px]:w-auto bg-white font-bold text-gray-800 rounded-lg hover:bg-white/90 py-[11px] px-[30px]\",\n                  children: \"Shop Sale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"20% OFF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"On selected figures\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"FREE SHIPPING\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"On orders over $50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"GUARANTEE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"30-day money back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white text-red-600 rounded-lg shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 flex flex-col items-center text-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-10 w-10 mb-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold\",\n                  children: \"REWARDS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"Earn points with every purchase\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"m5rcJYd6Nu8TwSKRQpEC+CiRAsY=\", false, function () {\n  return [useCart];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "productsAPI", "categoriesAPI", "useCart", "jsxDEV", "_jsxDEV", "Home", "_s", "featuredProducts", "setFeaturedProducts", "categories", "setCategories", "loading", "setLoading", "addToCart", "loadData", "productsResponse", "categoriesResponse", "Promise", "all", "getAll", "limit", "data", "products", "error", "console", "handleAddToCart", "productId", "result", "success", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "to", "length", "map", "category", "id", "style", "backgroundImage", "image", "name", "product_count", "product", "image_path", "category_name", "Number", "price", "toFixed", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/KyoPal/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { productsAPI, categoriesAPI } from '../services/api';\nimport { useCart } from '../contexts/CartContext';\n\nconst Home = () => {\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const { addToCart } = useCart();\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [productsResponse, categoriesResponse] = await Promise.all([\n        productsAPI.getAll({ limit: 8 }),\n        categoriesAPI.getAll()\n      ]);\n      \n      setFeaturedProducts(productsResponse.data.products || []);\n      setCategories(categoriesResponse.data.categories || []);\n    } catch (error) {\n      console.error('Failed to load data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddToCart = async (productId) => {\n    const result = await addToCart(productId);\n    if (result.success) {\n      // You could add a toast notification here\n      console.log('Product added to cart');\n    } else {\n      console.error('Failed to add to cart:', result.error);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Hero Section */}\n      <section className=\"relative w-full min-h-[400px] py-10 md:py-14 overflow-hidden\">\n        <img \n          src=\"/background.jpg\" \n          className=\"absolute inset-0 w-full h-full object-cover opacity-5 z-0\" \n          alt=\"background\" \n        />\n\n        <div className=\"container relative z-10 text-center md:text-left space-y-6 md:space-y-10\">\n          <h1 className=\"text-red-600 text-md md:text-2xl font-bold\">\n            FIRST ANIME STORE IN PALESTINE!\n          </h1>\n          <p className=\"text-black font-extrabold text-3xl md:text-4xl\">\n            Discover World of <span className=\"text-red-600\">Anime</span> Collectibles\n          </p>\n          <p className=\"text-black/70 text-lg md:w-2/3\">\n            Explore our premium selection of anime merchandise, figures, and collectibles. Find your favorite characters and series.\n          </p>\n          <div className=\"flex flex-wrap justify-center md:justify-start gap-4\">\n            <Link \n              to=\"/products\" \n              className=\"bg-red-600 px-8 py-3 text-white font-bold rounded-lg text-md hover:bg-red-500 transition-transform shadow-md\"\n            >\n              Shop Now\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      {categories.length > 0 && (\n        <section className=\"container w-full text-center pt-12\">\n          <p className=\"text-3xl font-extrabold\">Shop by Category</p>\n          <p className=\"text-gray-700 mt-4\">Explore our wide range of anime merchandise categories</p>\n          <section className=\"mt-8 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pb-4\">\n            {categories.map((category) => (\n              <Link key={category.id} to={`/products?category=${category.id}`} className=\"group\">\n                <article \n                  className=\"bg-black bg-opacity-80 shadow-md rounded-xl p-4 text-left h-40 bg-cover bg-center w-full mb-4 group-hover:bg-opacity-70 transition-all duration-300\"\n                  style={{\n                    backgroundImage: `url('${category.image || '/images/website/Kyo2.jpg'}')`\n                  }}\n                >\n                  <div className=\"relative top-20\">\n                    <p className=\"text-lg font-extrabold text-white\">{category.name}</p>\n                    <p className=\"text-sm text-white mt-2\">{category.product_count || 0} products</p>\n                  </div>\n                </article>\n              </Link>\n            ))}\n          </section>\n        </section>\n      )}\n\n      {/* Featured Products Section */}\n      {featuredProducts.length > 0 && (\n        <section className=\"container pt-12\">\n          <div className=\"text-center space-y-2\">\n            <p className=\"inline-block bg-red-600 text-white text-sm font-semibold px-4 py-1 rounded-full shadow-sm\">\n              Trending Now\n            </p>\n            <p className=\"text-3xl font-extrabold text-gray-800\">\n              Featured Products\n            </p>\n          </div>\n\n          <section className=\"mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {featuredProducts.map((product) => (\n              <article key={product.id} className=\"group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto\">\n                <div className=\"overflow-hidden\">\n                  <img \n                    src={product.image_path || '/images/website/Kyo2.jpg'} \n                    alt={product.name}\n                    className=\"object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105\"\n                  />\n                </div>\n                <div className=\"p-4 flex flex-col justify-between flex-1\">\n                  <div>\n                    <p className=\"text-xs text-red-500 uppercase font-semibold\">\n                      {product.category_name || 'Uncategorized'}\n                    </p>\n                    <p className=\"text-sm font-bold text-gray-800 truncate\">{product.name}</p>\n                    <p className=\"text-sm text-gray-600\">\n                      ${Number(product.price).toFixed(2)}\n                    </p>\n                  </div>\n                  <div className=\"mt-4 flex gap-2\">\n                    <button\n                      onClick={() => handleAddToCart(product.id)}\n                      className=\"flex-1 bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200\"\n                    >\n                      Add to Cart\n                    </button>\n                    <Link\n                      to={`/product/${product.id}`}\n                      className=\"px-3 py-2 border border-red-600 text-red-600 rounded-lg text-sm hover:bg-red-50 transition-colors duration-200 flex items-center justify-center\"\n                    >\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              </article>\n            ))}\n          </section>\n          \n          <div className=\"flex items-center justify-center mt-12\">\n            <Link \n              to=\"/products\" \n              className=\"flex items-center bg-white text-black rounded-md text-sm py-[10px] px-[20px] border border-gray-200 hover:bg-red-50 transition-transform duration-300 ease-in-out\"\n            >\n              View All Products\n              <svg className=\"w-5 h-5 ml-2\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\">\n                <path stroke=\"#000000\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"m19 12-6-6m6 6-6 6m6-6H5\"/>\n              </svg>\n            </Link>\n          </div>\n        </section>\n      )}\n\n      {/* Special Offers Section */}\n      <section className=\"py-12 bg-red-600 text-white mt-20\">\n        <div className=\"container\">\n          <div className=\"grid gap-6 lg:grid-cols-2 lg:gap-12 items-center\">\n            <div className=\"space-y-4\">\n              <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">Special Offers</h2>\n              <p className=\"max-w-[600px] md:text-xl\">\n                Limited time offers on selected anime merchandise. Don't miss out on these exclusive deals!\n              </p>\n              <div className=\"flex flex-col gap-2 min-[400px]:flex-row\">\n                <Link to=\"/products?sale=true\" className=\"inline-block\">\n                  <button className=\"w-full min-[400px]:w-auto bg-white font-bold text-gray-800 rounded-lg hover:bg-white/90 py-[11px] px-[30px]\">\n                    Shop Sale\n                  </button>\n                </Link>\n              </div>\n            </div>\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">20% OFF</h3>\n                  <p className=\"text-sm\">On selected figures</p>\n                </div>\n              </div>\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">FREE SHIPPING</h3>\n                  <p className=\"text-sm\">On orders over $50</p>\n                </div>\n              </div>\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">GUARANTEE</h3>\n                  <p className=\"text-sm\">30-day money back</p>\n                </div>\n              </div>\n              <div className=\"bg-white text-red-600 rounded-lg shadow-md\">\n                <div className=\"p-6 flex flex-col items-center text-center space-y-2\">\n                  <svg className=\"h-10 w-10 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"></path>\n                  </svg>\n                  <h3 className=\"font-bold\">REWARDS</h3>\n                  <p className=\"text-sm\">Earn points with every purchase</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,EAAEC,aAAa,QAAQ,iBAAiB;AAC5D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM;IAAEgB;EAAU,CAAC,GAAGX,OAAO,CAAC,CAAC;EAE/BJ,SAAS,CAAC,MAAM;IACdgB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACG,gBAAgB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DlB,WAAW,CAACmB,MAAM,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC,EAChCnB,aAAa,CAACkB,MAAM,CAAC,CAAC,CACvB,CAAC;MAEFX,mBAAmB,CAACO,gBAAgB,CAACM,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC;MACzDZ,aAAa,CAACM,kBAAkB,CAACK,IAAI,CAACZ,UAAU,IAAI,EAAE,CAAC;IACzD,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,eAAe,GAAG,MAAOC,SAAS,IAAK;IAC3C,MAAMC,MAAM,GAAG,MAAMd,SAAS,CAACa,SAAS,CAAC;IACzC,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClB;MACAJ,OAAO,CAACK,GAAG,CAAC,uBAAuB,CAAC;IACtC,CAAC,MAAM;MACLL,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEI,MAAM,CAACJ,KAAK,CAAC;IACvD;EACF,CAAC;EAED,IAAIZ,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK0B,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D3B,OAAA;QAAK0B,SAAS,EAAC;MAA+D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CAAC;EAEV;EAEA,oBACE/B,OAAA;IAAA2B,QAAA,gBAEE3B,OAAA;MAAS0B,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC/E3B,OAAA;QACEgC,GAAG,EAAC,iBAAiB;QACrBN,SAAS,EAAC,2DAA2D;QACrEO,GAAG,EAAC;MAAY;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEF/B,OAAA;QAAK0B,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACvF3B,OAAA;UAAI0B,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/B,OAAA;UAAG0B,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,oBAC1C,eAAA3B,OAAA;YAAM0B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,iBAC/D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/B,OAAA;UAAG0B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAE9C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/B,OAAA;UAAK0B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnE3B,OAAA,CAACL,IAAI;YACHuC,EAAE,EAAC,WAAW;YACdR,SAAS,EAAC,8GAA8G;YAAAC,QAAA,EACzH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGT1B,UAAU,CAAC8B,MAAM,GAAG,CAAC,iBACpBnC,OAAA;MAAS0B,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBACrD3B,OAAA;QAAG0B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC3D/B,OAAA;QAAG0B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAsD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5F/B,OAAA;QAAS0B,SAAS,EAAC,+EAA+E;QAAAC,QAAA,EAC/FtB,UAAU,CAAC+B,GAAG,CAAEC,QAAQ,iBACvBrC,OAAA,CAACL,IAAI;UAAmBuC,EAAE,EAAE,sBAAsBG,QAAQ,CAACC,EAAE,EAAG;UAACZ,SAAS,EAAC,OAAO;UAAAC,QAAA,eAChF3B,OAAA;YACE0B,SAAS,EAAC,qJAAqJ;YAC/Ja,KAAK,EAAE;cACLC,eAAe,EAAE,QAAQH,QAAQ,CAACI,KAAK,IAAI,0BAA0B;YACvE,CAAE;YAAAd,QAAA,eAEF3B,OAAA;cAAK0B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3B,OAAA;gBAAG0B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEU,QAAQ,CAACK;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE/B,OAAA;gBAAG0B,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,GAAEU,QAAQ,CAACM,aAAa,IAAI,CAAC,EAAC,WAAS;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC,GAXDM,QAAQ,CAACC,EAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYhB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACV,EAGA5B,gBAAgB,CAACgC,MAAM,GAAG,CAAC,iBAC1BnC,OAAA;MAAS0B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAClC3B,OAAA;QAAK0B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC3B,OAAA;UAAG0B,SAAS,EAAC,2FAA2F;UAAAC,QAAA,EAAC;QAEzG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/B,OAAA;UAAG0B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN/B,OAAA;QAAS0B,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EAC3ExB,gBAAgB,CAACiC,GAAG,CAAEQ,OAAO,iBAC5B5C,OAAA;UAA0B0B,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAChI3B,OAAA;YAAK0B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B3B,OAAA;cACEgC,GAAG,EAAEY,OAAO,CAACC,UAAU,IAAI,0BAA2B;cACtDZ,GAAG,EAAEW,OAAO,CAACF,IAAK;cAClBhB,SAAS,EAAC;YAAuG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/B,OAAA;YAAK0B,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvD3B,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAG0B,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACxDiB,OAAO,CAACE,aAAa,IAAI;cAAe;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACJ/B,OAAA;gBAAG0B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEiB,OAAO,CAACF;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E/B,OAAA;gBAAG0B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,GAClC,EAACoB,MAAM,CAACH,OAAO,CAACI,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN/B,OAAA;cAAK0B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3B,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAACuB,OAAO,CAACN,EAAE,CAAE;gBAC3CZ,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,EACjH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/B,OAAA,CAACL,IAAI;gBACHuC,EAAE,EAAE,YAAYU,OAAO,CAACN,EAAE,EAAG;gBAC7BZ,SAAS,EAAC,iJAAiJ;gBAAAC,QAAA,eAE3J3B,OAAA;kBAAKmD,KAAK,EAAC,4BAA4B;kBAACzB,SAAS,EAAC,SAAS;kBAAC0B,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAA3B,QAAA,gBAC/G3B,OAAA;oBAAMuD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAkC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1G/B,OAAA;oBAAMuD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAyH;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAnCMa,OAAO,CAACN,EAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoCf,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEV/B,OAAA;QAAK0B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD3B,OAAA,CAACL,IAAI;UACHuC,EAAE,EAAC,WAAW;UACdR,SAAS,EAAC,mKAAmK;UAAAC,QAAA,GAC9K,mBAEC,eAAA3B,OAAA;YAAK0B,SAAS,EAAC,cAAc;YAAC2B,OAAO,EAAC,WAAW;YAACF,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAAAzB,QAAA,eAC9F3B,OAAA;cAAMsD,MAAM,EAAC,SAAS;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,CAAC,EAAC;YAA0B;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACV,eAGD/B,OAAA;MAAS0B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eACpD3B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3B,OAAA;UAAK0B,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/D3B,OAAA;YAAK0B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3B,OAAA;cAAI0B,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/F/B,OAAA;cAAG0B,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ/B,OAAA;cAAK0B,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvD3B,OAAA,CAACL,IAAI;gBAACuC,EAAE,EAAC,qBAAqB;gBAACR,SAAS,EAAC,cAAc;gBAAAC,QAAA,eACrD3B,OAAA;kBAAQ0B,SAAS,EAAC,6GAA6G;kBAAAC,QAAA,EAAC;gBAEhI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/B,OAAA;YAAK0B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC3B,OAAA;cAAK0B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzD3B,OAAA;gBAAK0B,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE3B,OAAA;kBAAK0B,SAAS,EAAC,gBAAgB;kBAAC0B,IAAI,EAAC,MAAM;kBAACE,MAAM,EAAC,cAAc;kBAACD,OAAO,EAAC,WAAW;kBAACF,KAAK,EAAC,4BAA4B;kBAAAxB,QAAA,eACtH3B,OAAA;oBAAMuD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAA4I;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtN,CAAC,eACN/B,OAAA;kBAAI0B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtC/B,OAAA;kBAAG0B,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/B,OAAA;cAAK0B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzD3B,OAAA;gBAAK0B,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE3B,OAAA;kBAAK0B,SAAS,EAAC,gBAAgB;kBAAC0B,IAAI,EAAC,MAAM;kBAACE,MAAM,EAAC,cAAc;kBAACD,OAAO,EAAC,WAAW;kBAACF,KAAK,EAAC,4BAA4B;kBAAAxB,QAAA,gBACtH3B,OAAA;oBAAM0D,CAAC,EAAC;kBAAiE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjF/B,OAAA;oBAAMuD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAyP;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnU,CAAC,eACN/B,OAAA;kBAAI0B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5C/B,OAAA;kBAAG0B,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/B,OAAA;cAAK0B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzD3B,OAAA;gBAAK0B,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE3B,OAAA;kBAAK0B,SAAS,EAAC,gBAAgB;kBAAC0B,IAAI,EAAC,MAAM;kBAACE,MAAM,EAAC,cAAc;kBAACD,OAAO,EAAC,WAAW;kBAACF,KAAK,EAAC,4BAA4B;kBAAAxB,QAAA,eACtH3B,OAAA;oBAAMuD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAgM;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1Q,CAAC,eACN/B,OAAA;kBAAI0B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxC/B,OAAA;kBAAG0B,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/B,OAAA;cAAK0B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzD3B,OAAA;gBAAK0B,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE3B,OAAA;kBAAK0B,SAAS,EAAC,gBAAgB;kBAAC0B,IAAI,EAAC,MAAM;kBAACE,MAAM,EAAC,cAAc;kBAACD,OAAO,EAAC,WAAW;kBAACF,KAAK,EAAC,4BAA4B;kBAAAxB,QAAA,eACtH3B,OAAA;oBAAMuD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAyW;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnb,CAAC,eACN/B,OAAA;kBAAI0B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtC/B,OAAA;kBAAG0B,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAtOID,IAAI;EAAA,QAIcH,OAAO;AAAA;AAAA6D,EAAA,GAJzB1D,IAAI;AAwOV,eAAeA,IAAI;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}