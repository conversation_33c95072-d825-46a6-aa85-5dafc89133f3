# KyoPal - Anime Store

A modern anime merchandise e-commerce website built with React frontend and PHP backend.

## Features

- **Frontend (React)**
  - Modern responsive design with Tailwind CSS
  - User authentication and authorization
  - Product browsing and search
  - Shopping cart functionality
  - User account management
  - Admin dashboard
  - Mobile-friendly interface

- **Backend (PHP)**
  - RESTful API with mysqli (procedural style)
  - JWT authentication
  - Product and category management
  - Order processing
  - User management
  - Admin functionality

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- PHP (v7.4 or higher)
- MySQL/MariaDB
- Web server (Apache/Nginx) or PHP built-in server

### Database Setup

1. Create a MySQL database named `kyopal`
2. Import the database schema:
   ```sql
   mysql -u root -p kyopal < database.sql
   ```

### Backend Setup

1. Navigate to the backend directory
2. Update database credentials in `config/database.php` if needed
3. Run the sample data script to populate the database:
   ```bash
   php sample_data.php
   ```
4. Start PHP server (if not using Apache/Nginx):
   ```bash
   php -S localhost:8000
   ```

### Frontend Setup

1. Navigate to the frontend directory
2. Install dependencies:
   ```bash
   npm install
   ```
3. Update the API URL in `.env` if needed
4. Start the development server:
   ```bash
   npm start
   ```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000 (or your web server URL)

## Default Login Credentials

- **Admin**: <EMAIL> / admin123
- **User**: <EMAIL> / user123

## API Endpoints

### Authentication
- `POST /auth/login.php` - User login
- `POST /auth/register.php` - User registration
- `POST /auth/logout.php` - User logout
- `GET /auth/profile.php` - Get user profile
- `PUT /auth/profile.php` - Update user profile

### Products
- `GET /products/index.php` - Get all products
- `GET /products/show.php?id={id}` - Get product details
- `POST /products/create.php` - Create product (admin only)

### Categories
- `GET /categories/index.php` - Get all categories
- `POST /categories/create.php` - Create category (admin only)

### Cart
- `GET /cart/index.php` - Get cart items (authenticated)
- `POST /cart/add.php` - Add item to cart (authenticated)

### Admin
- `GET /admin/dashboard.php` - Get dashboard stats (admin only)

## Project Structure

```
KyoPal/
├── frontend/                 # React frontend
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── contexts/       # React contexts
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── backend/                 # PHP backend
│   ├── auth/               # Authentication endpoints
│   ├── products/           # Product endpoints
│   ├── categories/         # Category endpoints
│   ├── cart/               # Cart endpoints
│   ├── admin/              # Admin endpoints
│   └── config/             # Configuration files
└── database.sql            # Database schema
```

## Technologies Used

### Frontend
- React 19
- React Router DOM
- Tailwind CSS
- Axios
- Context API for state management

### Backend
- PHP 7.4+
- MySQL/MariaDB
- mysqli (procedural style)
- JWT for authentication

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
