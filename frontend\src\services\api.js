import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login.php', credentials),
  register: (userData) => api.post('/auth/register.php', userData),
  logout: () => api.post('/auth/logout.php'),
  getProfile: () => api.get('/auth/profile.php'),
  updateProfile: (userData) => api.put('/auth/profile.php', userData),
};

// Products API
export const productsAPI = {
  getAll: (params) => api.get('/products/index.php', { params }),
  getById: (id) => api.get(`/products/show.php?id=${id}`),
  create: (productData) => api.post('/products/create.php', productData),
  update: (id, productData) => api.put(`/products/update.php?id=${id}`, productData),
  delete: (id) => api.delete(`/products/delete.php?id=${id}`),
  search: (query) => api.get(`/products/search.php?q=${query}`),
};

// Categories API
export const categoriesAPI = {
  getAll: () => api.get('/categories/index.php'),
  getById: (id) => api.get(`/categories/show.php?id=${id}`),
  create: (categoryData) => api.post('/categories/create.php', categoryData),
  update: (id, categoryData) => api.put(`/categories/update.php?id=${id}`, categoryData),
  delete: (id) => api.delete(`/categories/delete.php?id=${id}`),
};

// Cart API
export const cartAPI = {
  get: () => api.get('/cart/index.php'),
  add: (productId, quantity = 1) => api.post('/cart/add.php', { product_id: productId, quantity }),
  update: (productId, quantity) => api.put('/cart/update.php', { product_id: productId, quantity }),
  remove: (productId) => api.delete(`/cart/remove.php?product_id=${productId}`),
  clear: () => api.delete('/cart/clear.php'),
};

// Orders API
export const ordersAPI = {
  getAll: () => api.get('/orders/index.php'),
  getById: (id) => api.get(`/orders/show.php?id=${id}`),
  create: (orderData) => api.post('/orders/create.php', orderData),
  update: (id, orderData) => api.put(`/orders/update.php?id=${id}`, orderData),
  getUserOrders: () => api.get('/orders/user.php'),
};

// Favorites API
export const favoritesAPI = {
  getAll: () => api.get('/favorites/index.php'),
  add: (productId) => api.post('/favorites/add.php', { product_id: productId }),
  remove: (productId) => api.delete(`/favorites/remove.php?product_id=${productId}`),
};

// Contact API
export const contactAPI = {
  send: (messageData) => api.post('/contact/send.php', messageData),
  getAll: () => api.get('/contact/index.php'),
  markAsRead: (id) => api.put(`/contact/read.php?id=${id}`),
};

// Admin API
export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard.php'),
  getUsers: () => api.get('/admin/users.php'),
  updateUser: (id, userData) => api.put(`/admin/users.php?id=${id}`, userData),
  deleteUser: (id) => api.delete(`/admin/users.php?id=${id}`),
  getSettings: () => api.get('/admin/settings.php'),
  updateSettings: (settings) => api.put('/admin/settings.php', settings),
};

export default api;
