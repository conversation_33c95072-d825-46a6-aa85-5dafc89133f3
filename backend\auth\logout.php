<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_error_response('Method not allowed', 405);
}

// For now, logout is handled on the client side by removing the token
// In a more advanced implementation, you could maintain a blacklist of tokens

send_success_response([], 'Logout successful');
?>
