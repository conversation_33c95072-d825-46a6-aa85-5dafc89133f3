<?php
require_once 'config/database.php';
require_once 'config/utils.php';

$connection = get_db_connection();

// Insert sample categories
$categories = [
    ['Anime Figures', 'High-quality anime character figures and collectibles'],
    ['Manga Books', 'Original and translated manga books'],
    ['Dakimakura', 'Anime character body pillows and covers'],
    ['Keychains', 'Anime character keychains and accessories'],
    ['Posters', 'Anime posters and wall art'],
    ['Cosplay', 'Cosplay costumes and accessories']
];

echo "Inserting categories...\n";
foreach ($categories as $category) {
    $query = "INSERT INTO Categories (name, description) VALUES (?, ?)";
    execute_query($connection, $query, $category);
}

// Insert admin user
echo "Creating admin user...\n";
$admin_password = hash_password('admin123');
$query = "INSERT INTO Users (name, email, password, phone, region, city, address, role) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
execute_query($connection, $query, [
    'Admin User',
    '<EMAIL>',
    $admin_password,
    '+************',
    'west_bank',
    'Ramallah',
    'Admin Address',
    'admin'
]);

// Insert sample user
echo "Creating sample user...\n";
$user_password = hash_password('user123');
$query = "INSERT INTO Users (name, email, password, phone, region, city, address, role) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
execute_query($connection, $query, [
    'John Doe',
    '<EMAIL>',
    $user_password,
    '+************',
    'west_bank',
    'Nablus',
    'User Address',
    'user'
]);

// Insert sample products
echo "Inserting sample products...\n";
$products = [
    ['Naruto Uzumaki Figure', 'High-quality Naruto figure from the popular anime series', 29.99, 1, 10],
    ['Attack on Titan Manga Vol 1', 'First volume of the Attack on Titan manga series', 12.99, 2, 25],
    ['Anime Waifu Dakimakura', 'Comfortable anime character body pillow cover', 39.99, 3, 5],
    ['One Piece Keychain Set', 'Set of 5 One Piece character keychains', 15.99, 4, 20],
    ['Studio Ghibli Poster', 'Beautiful Studio Ghibli movie poster', 8.99, 5, 15],
    ['Sailor Moon Cosplay Outfit', 'Complete Sailor Moon cosplay costume', 89.99, 6, 3],
    ['Dragon Ball Z Goku Figure', 'Super Saiyan Goku collectible figure', 34.99, 1, 8],
    ['Death Note Manga Complete Set', 'Complete Death Note manga collection', 79.99, 2, 5],
];

foreach ($products as $product) {
    $query = "INSERT INTO Products (name, description, price, category_id, stock) VALUES (?, ?, ?, ?, ?)";
    execute_query($connection, $query, $product);
}

// Insert settings
echo "Inserting settings...\n";
$query = "INSERT INTO Settings (id, west_bank_shipping_fee, occupied_territories_shipping_fee, free_shipping_threshold) VALUES (1, 20, 50, 500)";
execute_query($connection, $query);

close_db_connection($connection);

echo "Sample data inserted successfully!\n";
echo "Admin login: <EMAIL> / admin123\n";
echo "User login: <EMAIL> / user123\n";
?>
