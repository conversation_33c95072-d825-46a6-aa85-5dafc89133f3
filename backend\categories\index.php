<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    send_error_response('Method not allowed', 405);
}

$connection = get_db_connection();

// Get categories with product count
$query = "
    SELECT 
        c.id,
        c.name,
        c.description,
        c.image,
        c.created_at,
        COUNT(p.id) as product_count
    FROM Categories c
    LEFT JOIN Products p ON c.id = p.category_id
    GROUP BY c.id
    ORDER BY c.name
";

$result = execute_query($connection, $query);
$categories = fetch_all($result);

close_db_connection($connection);

send_success_response(['categories' => $categories]);
?>
