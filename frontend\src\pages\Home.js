import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { productsAPI, categoriesAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const { addToCart } = useCart();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsResponse, categoriesResponse] = await Promise.all([
        productsAPI.getAll({ limit: 8 }),
        categoriesAPI.getAll()
      ]);
      console.log(productsResponse.data.data.products);
      setFeaturedProducts(productsResponse.data.products || []);
      setCategories(categoriesResponse.data.categories || []);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async (productId) => {
    const result = await addToCart(productId);
    if (result.success) {
      // You could add a toast notification here
      console.log('Product added to cart');
    } else {
      console.error('Failed to add to cart:', result.error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative w-full min-h-[400px] py-10 md:py-14 overflow-hidden">
        <img 
          src="/background.jpg" 
          className="absolute inset-0 w-full h-full object-cover opacity-5 z-0" 
          alt="background" 
        />

        <div className="container relative z-10 text-center md:text-left space-y-6 md:space-y-10">
          <h1 className="text-red-600 text-md md:text-2xl font-bold">
            FIRST ANIME STORE IN PALESTINE!
          </h1>
          <p className="text-black font-extrabold text-3xl md:text-4xl">
            Discover World of <span className="text-red-600">Anime</span> Collectibles
          </p>
          <p className="text-black/70 text-lg md:w-2/3">
            Explore our premium selection of anime merchandise, figures, and collectibles. Find your favorite characters and series.
          </p>
          <div className="flex flex-wrap justify-center md:justify-start gap-4">
            <Link 
              to="/products" 
              className="bg-red-600 px-8 py-3 text-white font-bold rounded-lg text-md hover:bg-red-500 transition-transform shadow-md"
            >
              Shop Now
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      {categories.length > 0 && (
        <section className="container w-full text-center pt-12">
          <p className="text-3xl font-extrabold">Shop by Category</p>
          <p className="text-gray-700 mt-4">Explore our wide range of anime merchandise categories</p>
          <section className="mt-8 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pb-4">
            {categories.map((category) => (
              <Link key={category.id} to={`/products?category=${category.id}`} className="group">
                <article 
                  className="bg-black bg-opacity-80 shadow-md rounded-xl p-4 text-left h-40 bg-cover bg-center w-full mb-4 group-hover:bg-opacity-70 transition-all duration-300"
                  style={{
                    backgroundImage: `url('${category.image || '/images/website/Kyo2.jpg'}')`
                  }}
                >
                  <div className="relative top-20">
                    <p className="text-lg font-extrabold text-white">{category.name}</p>
                    <p className="text-sm text-white mt-2">{category.product_count || 0} products</p>
                  </div>
                </article>
              </Link>
            ))}
          </section>
        </section>
      )}

      {/* Featured Products Section */}
      {featuredProducts.length > 0 && (
        <section className="container pt-12">
          <div className="text-center space-y-2">
            <p className="inline-block bg-red-600 text-white text-sm font-semibold px-4 py-1 rounded-full shadow-sm">
              Trending Now
            </p>
            <p className="text-3xl font-extrabold text-gray-800">
              Featured Products
            </p>
          </div>

          <section className="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <article key={product.id} className="group bg-white shadow-lg rounded-lg overflow-hidden flex flex-col w-full sm:max-w-sm mx-auto">
                <div className="overflow-hidden">
                  <img 
                    src={product.image_path || '/images/website/Kyo2.jpg'} 
                    alt={product.name}
                    className="object-cover w-full aspect-square transition-transform duration-300 ease-in-out group-hover:scale-105"
                  />
                </div>
                <div className="p-4 flex flex-col justify-between flex-1">
                  <div>
                    <p className="text-xs text-red-500 uppercase font-semibold">
                      {product.category_name || 'Uncategorized'}
                    </p>
                    <p className="text-sm font-bold text-gray-800 truncate">{product.name}</p>
                    <p className="text-sm text-gray-600">
                      ${Number(product.price).toFixed(2)}
                    </p>
                  </div>
                  <div className="mt-4 flex gap-2">
                    <button
                      onClick={() => handleAddToCart(product.id)}
                      className="flex-1 bg-red-600 text-white py-2 rounded-lg text-sm hover:bg-red-500 transition-colors duration-200"
                    >
                      Add to Cart
                    </button>
                    <Link
                      to={`/product/${product.id}`}
                      className="px-3 py-2 border border-red-600 text-red-600 rounded-lg text-sm hover:bg-red-50 transition-colors duration-200 flex items-center justify-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </section>
          
          <div className="flex items-center justify-center mt-12">
            <Link 
              to="/products" 
              className="flex items-center bg-white text-black rounded-md text-sm py-[10px] px-[20px] border border-gray-200 hover:bg-red-50 transition-transform duration-300 ease-in-out"
            >
              View All Products
              <svg className="w-5 h-5 ml-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none">
                <path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 12-6-6m6 6-6 6m6-6H5"/>
              </svg>
            </Link>
          </div>
        </section>
      )}

      {/* Special Offers Section */}
      <section className="py-12 bg-red-600 text-white mt-20">
        <div className="container">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Special Offers</h2>
              <p className="max-w-[600px] md:text-xl">
                Limited time offers on selected anime merchandise. Don't miss out on these exclusive deals!
              </p>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Link to="/products?sale=true" className="inline-block">
                  <button className="w-full min-[400px]:w-auto bg-white font-bold text-gray-800 rounded-lg hover:bg-white/90 py-[11px] px-[30px]">
                    Shop Sale
                  </button>
                </Link>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white text-red-600 rounded-lg shadow-md">
                <div className="p-6 flex flex-col items-center text-center space-y-2">
                  <svg className="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                  </svg>
                  <h3 className="font-bold">20% OFF</h3>
                  <p className="text-sm">On selected figures</p>
                </div>
              </div>
              <div className="bg-white text-red-600 rounded-lg shadow-md">
                <div className="p-6 flex flex-col items-center text-center space-y-2">
                  <svg className="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"></path>
                  </svg>
                  <h3 className="font-bold">FREE SHIPPING</h3>
                  <p className="text-sm">On orders over $50</p>
                </div>
              </div>
              <div className="bg-white text-red-600 rounded-lg shadow-md">
                <div className="p-6 flex flex-col items-center text-center space-y-2">
                  <svg className="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                  <h3 className="font-bold">GUARANTEE</h3>
                  <p className="text-sm">30-day money back</p>
                </div>
              </div>
              <div className="bg-white text-red-600 rounded-lg shadow-md">
                <div className="p-6 flex flex-col items-center text-center space-y-2">
                  <svg className="h-10 w-10 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                  </svg>
                  <h3 className="font-bold">REWARDS</h3>
                  <p className="text-sm">Earn points with every purchase</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
