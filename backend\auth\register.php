<?php
require_once '../config/database.php';
require_once '../config/utils.php';

set_cors_headers();
set_json_header();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_error_response('Method not allowed', 405);
}

$input = get_json_input();

// Validate required fields
$required_fields = ['name', 'email', 'password', 'phone', 'region', 'city', 'address'];
$missing_fields = validate_required_fields($input, $required_fields);

if (!empty($missing_fields)) {
    send_error_response('Missing required fields: ' . implode(', ', $missing_fields));
}

$name = sanitize_input($input['name']);
$email = sanitize_input($input['email']);
$password = $input['password'];
$phone = sanitize_input($input['phone']);
$region = sanitize_input($input['region']);
$city = sanitize_input($input['city']);
$address = sanitize_input($input['address']);

// Validate email format
if (!validate_email($email)) {
    send_error_response('Invalid email format');
}

// Validate password length
if (strlen($password) < 6) {
    send_error_response('Password must be at least 6 characters long');
}

// Validate region
if (!in_array($region, ['west_bank', 'occupied_territories'])) {
    send_error_response('Invalid region');
}

// Get database connection
$connection = get_db_connection();

// Check if email already exists
$query = "SELECT id FROM Users WHERE email = ?";
$result = execute_query($connection, $query, [$email]);

if (fetch_single($result)) {
    close_db_connection($connection);
    send_error_response('Email already exists');
}

// Hash password
$hashed_password = hash_password($password);

// Insert new user
$query = "INSERT INTO Users (name, email, password, phone, region, city, address, role) VALUES (?, ?, ?, ?, ?, ?, ?, 'user')";
$result = execute_query($connection, $query, [$name, $email, $hashed_password, $phone, $region, $city, $address]);

if (!$result) {
    close_db_connection($connection);
    send_error_response('Failed to create user');
}

$user_id = get_last_insert_id($connection);

// Get the created user
$query = "SELECT id, name, email, role FROM Users WHERE id = ?";
$result = execute_query($connection, $query, [$user_id]);
$user = fetch_single($result);

close_db_connection($connection);

// Generate JWT token
$token = generate_jwt_token($user['id'], $user['email']);

// Send success response
send_success_response([
    'token' => $token,
    'user' => $user
], 'Registration successful');
?>
