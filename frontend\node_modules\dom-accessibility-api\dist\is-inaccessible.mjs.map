{"version": 3, "file": "is-inaccessible.mjs", "names": ["isInaccessible", "element", "options", "getComputedStyle", "ownerDocument", "defaultView", "isSubtreeInaccessible", "isSubtreeInaccessibleImpl", "TypeError", "visibility", "currentElement", "parentElement", "hidden", "getAttribute", "display"], "sources": ["../sources/is-inaccessible.ts"], "sourcesContent": ["export interface IsInaccessibleOptions {\n\tgetComputedStyle?: typeof window.getComputedStyle;\n\t/**\n\t * Can be used to return cached results from previous isSubtreeInaccessible calls.\n\t */\n\tisSubtreeInaccessible?: (element: Element) => boolean;\n}\n\n/**\n * Partial implementation https://www.w3.org/TR/wai-aria-1.2/#tree_exclusion\n * which should only be used for elements with a non-presentational role i.e.\n * `role=\"none\"` and `role=\"presentation\"` will not be excluded.\n *\n * Implements aria-hidden semantics (i.e. parent overrides child)\n * Ignores \"Child Presentational: True\" characteristics\n *\n * @param element\n * @param options\n * @returns {boolean} true if excluded, otherwise false\n */\nexport function isInaccessible(\n\telement: Element,\n\toptions: IsInaccessibleOptions = {}\n): boolean {\n\tconst {\n\t\tgetComputedStyle = element.ownerDocument.defaultView?.getComputedStyle,\n\t\tisSubtreeInaccessible: isSubtreeInaccessibleImpl = isSubtreeInaccessible,\n\t} = options;\n\tif (typeof getComputedStyle !== \"function\") {\n\t\tthrow new TypeError(\n\t\t\t\"Owner document of the element needs to have an associated window.\"\n\t\t);\n\t}\n\t// since visibility is inherited we can exit early\n\tif (getComputedStyle(element).visibility === \"hidden\") {\n\t\treturn true;\n\t}\n\n\tlet currentElement: Element | null = element;\n\twhile (currentElement) {\n\t\tif (isSubtreeInaccessibleImpl(currentElement, { getComputedStyle })) {\n\t\t\treturn true;\n\t\t}\n\n\t\tcurrentElement = currentElement.parentElement;\n\t}\n\n\treturn false;\n}\n\nexport interface IsSubtreeInaccessibleOptions {\n\tgetComputedStyle?: typeof window.getComputedStyle;\n}\n\n/**\n *\n * @param element\n * @param options\n * @returns {boolean} - `true` if every child of the element is inaccessible\n */\nexport function isSubtreeInaccessible(\n\telement: Element,\n\toptions: IsSubtreeInaccessibleOptions = {}\n): boolean {\n\tconst {\n\t\tgetComputedStyle = element.ownerDocument.defaultView?.getComputedStyle,\n\t} = options;\n\tif (typeof getComputedStyle !== \"function\") {\n\t\tthrow new TypeError(\n\t\t\t\"Owner document of the element needs to have an associated window.\"\n\t\t);\n\t}\n\n\tif ((element as HTMLElement).hidden === true) {\n\t\treturn true;\n\t}\n\n\tif (element.getAttribute(\"aria-hidden\") === \"true\") {\n\t\treturn true;\n\t}\n\n\tif (getComputedStyle(element).display === \"none\") {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n"], "mappings": "AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAc,CAC7BC,OAAgB,EAEN;EAAA;EAAA,IADVC,OAA8B,uEAAG,CAAC,CAAC;EAEnC,4BAGIA,OAAO,CAFVC,gBAAgB;IAAhBA,gBAAgB,+DAAGF,OAAO,CAACG,aAAa,CAACC,WAAW,0DAAjC,sBAAmCF,gBAAgB;IAAA,wBAEnED,OAAO,CADVI,qBAAqB;IAAEC,yBAAyB,sCAAGD,qBAAqB;EAEzE,IAAI,OAAOH,gBAAgB,KAAK,UAAU,EAAE;IAC3C,MAAM,IAAIK,SAAS,CAClB,mEAAmE,CACnE;EACF;EACA;EACA,IAAIL,gBAAgB,CAACF,OAAO,CAAC,CAACQ,UAAU,KAAK,QAAQ,EAAE;IACtD,OAAO,IAAI;EACZ;EAEA,IAAIC,cAA8B,GAAGT,OAAO;EAC5C,OAAOS,cAAc,EAAE;IACtB,IAAIH,yBAAyB,CAACG,cAAc,EAAE;MAAEP,gBAAgB,EAAhBA;IAAiB,CAAC,CAAC,EAAE;MACpE,OAAO,IAAI;IACZ;IAEAO,cAAc,GAAGA,cAAc,CAACC,aAAa;EAC9C;EAEA,OAAO,KAAK;AACb;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASL,qBAAqB,CACpCL,OAAgB,EAEN;EAAA;EAAA,IADVC,OAAqC,uEAAG,CAAC,CAAC;EAE1C,6BAEIA,OAAO,CADVC,gBAAgB;IAAhBA,gBAAgB,iEAAGF,OAAO,CAACG,aAAa,CAACC,WAAW,2DAAjC,uBAAmCF,gBAAgB;EAEvE,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;IAC3C,MAAM,IAAIK,SAAS,CAClB,mEAAmE,CACnE;EACF;EAEA,IAAKP,OAAO,CAAiBW,MAAM,KAAK,IAAI,EAAE;IAC7C,OAAO,IAAI;EACZ;EAEA,IAAIX,OAAO,CAACY,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;IACnD,OAAO,IAAI;EACZ;EAEA,IAAIV,gBAAgB,CAACF,OAAO,CAAC,CAACa,OAAO,KAAK,MAAM,EAAE;IACjD,OAAO,IAAI;EACZ;EAEA,OAAO,KAAK;AACb"}